package com.ximalaya.galaxy.business.business.boss.sse

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.annotation.JSONField

/**
 *<AUTHOR>
 *@create 2025-07-03 14:09
 */
class SseCloseDto {

    var sessionCode: Long? = null

    var phaseCode: Long? = null

    @JSONField(serialize = false, deserialize = false)
    fun toJson(): String {
        return JSON.toJSONString(this)
    }

    companion object {

        @JvmStatic
        fun parseJson(json: String): SseCloseDto {
            return JSON.parseObject(json, SseCloseDto::class.java)
        }
    }

}