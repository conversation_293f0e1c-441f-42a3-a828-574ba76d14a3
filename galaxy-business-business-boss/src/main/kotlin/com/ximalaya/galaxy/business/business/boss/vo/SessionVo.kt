package com.ximalaya.galaxy.business.business.boss.vo

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity

/**
 *<AUTHOR>
 *@create 2025-05-22 21:55
 */
class SessionVo {

    var sessionCode: String? = null

    var sessionName: String? = null

    var businessData: Map<String, String>? = null

    var uid: Long? = null

    companion object {

        fun of(sessionEntity: GalaxySessionEntity): SessionVo {
            return SessionVo().apply {
                this.sessionCode = sessionEntity.sessionCode?.toString()
                this.sessionName = sessionEntity.sessionName
                this.businessData = sessionEntity.businessData?.let {
                    JSON.parseObject(
                        it,
                        object : TypeReference<Map<String, String>>() {})
                }
                this.uid = sessionEntity.uid
            }
        }

    }

}