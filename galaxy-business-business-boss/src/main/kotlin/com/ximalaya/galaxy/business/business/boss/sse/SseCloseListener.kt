package com.ximalaya.galaxy.business.business.boss.sse

import com.ximalaya.galaxy.business.business.boss.BizStreamMessage
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import org.redisson.api.RTopic
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2025-05-22 00:00
 */
@Component
class SseCloseListener(
    private val redissonClient: RedissonClient,
    private val bizStreamMessage: BizStreamMessage,
) : InitializingBean {

    private var closeTopic: RTopic? = null

    override fun afterPropertiesSet() {
        closeTopic = redissonClient.getTopic("sse-close-topic")

        closeTopic!!.addListener(String::class.java, { _, msg ->
            val closeDto = SseCloseDto.parseJson(msg.toString())

            bizStreamMessage.sendAndStopStream(
                closeDto.sessionCode!!, closeDto.phaseCode!!, ResultVo.failed<Unit>(
                    ErrorCode.SESSION_ERROR, "数据将输出至新窗口"
                )
            )
        })
    }

}