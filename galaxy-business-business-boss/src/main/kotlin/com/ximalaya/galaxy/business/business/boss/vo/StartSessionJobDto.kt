package com.ximalaya.galaxy.business.business.boss.vo

/**
 *<AUTHOR>
 *@create 2025-05-20 16:02
 */
data class StartSessionJobDto(

    var uid: Long,

    var sessionCode: Long,

    var phaseCode: Long,

    var systemPromptTemplate: String,

    var prompt: String,
) {

//  @JSONField(serialize = false, deserialize = false)
//  fun toStartSessionJobRequest(): StartSessionJobRequest {
//    // 注入sessionCode
//    val systemPrompt = PromptTemplate(systemPromptTemplate).render(mapOf("sessionCode" to sessionCode))
//
//    return StartSessionJobRequest.Builder()
//      .setUid(uid)
//      .setSessionCode(this.sessionCode)
//      .setPhaseCode(this.phaseCode)
//      .setSystemPrompt(systemPrompt)
//      .setPrompt(this.prompt)
//      .build()
//  }

}