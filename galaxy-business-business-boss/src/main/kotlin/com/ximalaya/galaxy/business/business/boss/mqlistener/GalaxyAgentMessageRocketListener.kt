package com.ximalaya.galaxy.business.business.boss.mqlistener

import com.ximalaya.galaxy.business.business.boss.BizStreamMessage
import com.ximalaya.galaxy.business.common.message.GalaxyMessage
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import mu.KotlinLogging
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener
import org.apache.rocketmq.spring.core.RocketMQListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/**
 *<AUTHOR>
 *@create 2025-05-21 23:34
 */
@Component
@RocketMQMessageListener(
    topic = GalaxyMessage.TOPIC,
    consumerGroup = GalaxyMessage.CONSUMER_GROUP
)
class GalaxyAgentMessageRocketListener(
    private val bizStreamMessage: BizStreamMessage,
) : RocketMQListener<GalaxyMessage> {

    @Transactional(rollbackFor = [Throwable::class])
    override fun onMessage(galaxyMessage: GalaxyMessage) {
        logger.debug("Receive RocketMQ Topic: Agent消息 id: {}, type: {}, content: {}", 
            galaxyMessage.id, galaxyMessage.type, galaxyMessage.content)

        // 从消息元数据中获取sessionCode和phaseCode
        val sessionCode = galaxyMessage.metadata["sessionCode"] as Long
        val phaseCode = galaxyMessage.metadata["phaseCode"] as Long
        val messageKey = galaxyMessage.metadata["messageKey"] as String

        logger.debug("Message metadata - sessionCode: {}, phaseCode: {}, messageKey: {}", 
            sessionCode, phaseCode, messageKey)

        // 解析AgentContentProtocol
        val agentMessage = AgentContentProtocol.parseJson(galaxyMessage.content)

        agentMessage._sessionCode = sessionCode
        agentMessage._phaseCode = phaseCode

        bizStreamMessage.sendSseWriteBackMessage(agentMessage)
    }



    companion object {
        private val logger = KotlinLogging.logger { }
    }

}