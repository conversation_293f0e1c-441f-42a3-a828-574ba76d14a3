package com.ximalaya.galaxy.business.business.boss

import com.ximalaya.galaxy.business.business.boss.vo.*
import com.ximalaya.galaxy.business.business.vo.QueryPageRequestVo
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 *<AUTHOR>
 *@create 2025-05-20 10:52
 */
interface BizGalaxyBoss {

    fun getSessionPhases(uid: Long, sessionCode: Long): SessionPhasesVo

    fun getPhaseCode(uid: Long, sessionCode: Long, phaseName: String): Long?

    fun connectSessionPhase(emitter: SseEmitter, uid: Long, sessionCode: Long, phaseCode: Long)

    fun createSessionJob(createDto: CreateSessionJobDto): Long

    fun startSessionJob(emitter: SseEmitter, startDto: StartSessionJobDto)

    fun startSessionJob(startDto: StartSessionJobWithPhaseNameDto): Boolean

    fun startToolJob(emitter: SseEmitter, startDto: StartToolJobDto)

    fun startToolJob(startDto: StartToolJobWithPhaseNameDto): Boolean

    fun updateBlockContent(sessionCode: Long, phaseCode: Long, blockCode: Long, content: String): Boolean

    fun getHistoricalSessions(uid: Long, pageRequest: QueryPageRequestVo): GalaxyPage<SessionVo>

    fun removeSession(uid: Long, sessionCode: Long): Boolean

    fun checkSessionUid(uid: Long, session: GalaxySessionEntity)

    fun checkSessionUid(emitter: SseEmitter, uid: Long, session: GalaxySessionEntity): Boolean
}