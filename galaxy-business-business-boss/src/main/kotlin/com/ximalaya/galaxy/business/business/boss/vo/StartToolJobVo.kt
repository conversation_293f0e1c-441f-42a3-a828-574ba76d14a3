package com.ximalaya.galaxy.business.business.boss.vo

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 *<AUTHOR>
 *@create 2025-05-30 15:53
 */
@ApiModel(description = "会话工具 VO")
class StartToolJobVo {

    @ApiModelProperty("阶段名称 声音的id")
    var phaseName: String? = null

    @ApiModelProperty("参数")
    var args: Map<String, String?>? = null

    @ApiModelProperty("需要删除的blockCode 刷新时需要将旧的call和result都删除 后端会校验")
    var needRemoveBlockCodes: List<Long>? = null

}