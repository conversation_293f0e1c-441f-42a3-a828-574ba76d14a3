package com.ximalaya.galaxy.business.business.boss.sse

import org.redisson.api.RTopic
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2025-05-22 00:00
 */
@Component
class SseCloseSender(
    private val redissonClient: RedissonClient,
) : InitializingBean {

    private var closeTopic: RTopic? = null

    fun sendClose(sessionCode: Long, phaseCode: Long) {
        closeTopic!!.publish(SseCloseDto().apply {
            this.sessionCode = sessionCode
            this.phaseCode = phaseCode
        }.toJson())
    }

    override fun afterPropertiesSet() {
        closeTopic = redissonClient.getTopic("sse-close-topic")
    }

}