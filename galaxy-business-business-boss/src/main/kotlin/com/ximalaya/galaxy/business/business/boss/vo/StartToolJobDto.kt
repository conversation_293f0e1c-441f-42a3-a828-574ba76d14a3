package com.ximalaya.galaxy.business.business.boss.vo

/**
 *<AUTHOR>
 *@create 2025-05-28 17:26
 */
data class StartToolJobDto(

  var uid: Long,

  var sessionCode: Long,

  var phaseCode: Long,

  var toolId: Long,

  var args: Map<String, String?>? = null,

  var needRemoveBlockCodes: List<Long>? = null,
) {

//  @JSONField(serialize = false, deserialize = false)
//  fun toStartToolJobRequest(): StartToolJobRequest {
//    return StartToolJobRequest.Builder()
//      .setUid(uid)
//      .setSessionCode(this.sessionCode)
//      .setPhaseCode(this.phaseCode)
//      .setToolId(this.toolId)
//      .setArgs(JSON.toJSONString(this.args))
//      .setNeedRemoveBlockCodes(if (CollectionUtils.isEmpty(this.needRemoveBlockCodes)) emptyList() else needRemoveBlockCodes)
//      .build()
//  }

}