package com.ximalaya.galaxy.business.business.boss

import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 *<AUTHOR>
 *@create 2025-06-05 21:32
 */
interface BizStreamMessage {

    fun reconnectStreamChannel(emitter: SseEmitter, sessionCode: Long, phaseCode: Long): Boolean

    fun connectRealtimeStreamChannel(emitter: SseEmitter, sessionCode: Long, phaseCode: Long): Boolean

    fun releaseStreamChannel(sessionCode: Long, phaseCode: Long)

    fun sendSseWriteBackMessage(agentMessage: AgentContentProtocol)

    fun <T> sendAndStopStream(sessionCode: Long, phaseCode: Long, resultVo: ResultVo<T>? = null)

    fun stop(sessionCode: Long, phaseCode: Long)

}