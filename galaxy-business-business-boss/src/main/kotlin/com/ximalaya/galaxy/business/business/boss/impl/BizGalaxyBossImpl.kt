package com.ximalaya.galaxy.business.business.boss.impl

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.BizStreamMessage
import com.ximalaya.galaxy.business.business.boss.BossNacosConfigBean
import com.ximalaya.galaxy.business.business.boss.vo.*
import com.ximalaya.galaxy.business.business.vo.QueryPageRequestVo
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import com.ximalaya.galaxy.business.repo.entity.AlbumEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.service.AlbumService
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.vo.AgentContentPing
import mu.KotlinLogging
import org.apache.commons.lang3.StringUtils
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-20 10:54
 */
@BusinessComponent
class BizGalaxyBossImpl(
    private val bizAlbumTrack: BizAlbumTrack,
    private val sessionService: GalaxySessionService,
    private val phaseService: GalaxyPhaseService,
    private val blockService: GalaxyBlockService,
    private val albumService: AlbumService,
//    private val workerJobIfaceService: GalaxyBusinessWorkerJobService.Iface,
    private val stringRedisTemplate: StringRedisTemplate,
    private val bizStreamMessage: BizStreamMessage,
    private val footballConfigBean: BossNacosConfigBean,
) : BizGalaxyBoss {

    override fun getSessionPhases(uid: Long, sessionCode: Long): SessionPhasesVo {
        // 管理员直接查询
        if (footballConfigBean.isAdmin(uid)) {
            val sessionEntity = sessionService.selectBySessionCode(sessionCode)
            GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
            checkSessionUid(uid, sessionEntity!!)

            val phaseEntities = phaseService.selectBySessionCode(sessionCode)
            return SessionPhasesVo.of(sessionEntity, phaseEntities)
        }

        val sessionEntity = sessionService.selectByUidAndSessionCode(uid, sessionCode)
        GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(uid, sessionEntity!!)

        val phaseEntities = phaseService.selectBySessionCode(sessionCode)
        return SessionPhasesVo.of(sessionEntity, phaseEntities)
    }

    override fun getPhaseCode(uid: Long, sessionCode: Long, phaseName: String): Long? {
        // 管理员直接查询
        if (footballConfigBean.isAdmin(uid)) {
            val phase = phaseService.selectBySessionCodeAndPhaseName(sessionCode, phaseName)
            return phase?.phaseCode
        }

        // 正常流程
        val sessionEntity = sessionService.selectByUidAndSessionCode(uid, sessionCode)
        GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(uid, sessionEntity!!)

        val phase = phaseService.selectBySessionCodeAndPhaseName(sessionCode, phaseName)
        return phase?.phaseCode
    }

    override fun connectSessionPhase(emitter: SseEmitter, uid: Long, sessionCode: Long, phaseCode: Long) {
        val session = if (footballConfigBean.isAdmin(uid)) {
            sessionService.selectBySessionCode(sessionCode)
        } else {
            sessionService.selectByUidAndSessionCode(uid, sessionCode)
        }

        if (session == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话不存在"))
            emitter.complete()
            return
        }
        // 检查uid是否与sessionCode匹配
        if (!checkSessionUid(emitter, uid, session)) {
            return
        }

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseCode(sessionCode, phaseCode)
        if (phase == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "阶段不存在"))
            emitter.complete()
            return
        }

        if (!bizStreamMessage.reconnectStreamChannel(emitter, sessionCode, phaseCode)) {
            return
        }

        if (StringUtils.isNotBlank(session.businessData)) {
            emitter.send(
                ResultVo.ok(
                    JSON.parseObject(
                        session.businessData,
                        object : TypeReference<Map<String, String>>() {})
                )
            )
        } else {
            emitter.send(ResultVo.ok<Unit>())
        }

        try {
            // 发送一个初始化消息触发历史消息处理
            val initMessage = AgentContentPing().apply {
                this._sessionCode = sessionCode
                this._phaseCode = phaseCode
                this.ts = System.currentTimeMillis()
            }
            bizStreamMessage.sendSseWriteBackMessage(initMessage)
        } catch (e: Exception) {
            logger.error("连接会话失败", e)

            // 连接会话失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            val result = ResultVo.failed<Unit>(pair.first, pair.second)
            emitter.send(result)
            emitter.complete()
            bizStreamMessage.releaseStreamChannel(sessionCode, phaseCode)
        }
    }

    override fun createSessionJob(createDto: CreateSessionJobDto): Long {
        try {
//            val thriftResponse =
//                workerJobIfaceService.createSessionJob(createDto.toCreateSessionJobRequest())
//
//            val response = CommonJobResponseConverter.transform(thriftResponse)
//
//            if (response.code != 200) {
//                logger.error(
//                    "创建任务失败, Phase: {}, Code: {}, Message: {}",
//                    createDto.phaseName,
//                    response.code,
//                    response.message
//                )
//
//                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "创建会话失败")
//            }
//
//            return response.data.toLong()

            return 0L
        } catch (e: Exception) {
            // 启动会话失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            throw GalaxyException(pair.first, pair.second)
        }
    }

    override fun startSessionJob(emitter: SseEmitter, startDto: StartSessionJobDto) {
        // 检查uid是否与sessionCode匹配
        val session = if (footballConfigBean.isAdmin(startDto.uid)) {
            sessionService.selectBySessionCode(startDto.sessionCode)
        } else {
            sessionService.selectByUidAndSessionCode(startDto.uid, startDto.sessionCode)
        }

        if (session == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话不存在"))
            emitter.complete()
            return
        }

        if (!checkSessionUid(emitter, startDto.uid, session)) {
            return
        }

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseCode(startDto.sessionCode, startDto.phaseCode)
        if (phase == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "阶段不存在"))
            emitter.complete()
            return
        }

        // 发送worker
        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(startDto.sessionCode, startDto.phaseCode)
        // 检查锁是否存在
        if (stringRedisTemplate.hasKey(ongoingRedisKey)) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", startDto.sessionCode, startDto.phaseCode)
            emitter.send(ResultVo.failed<Unit>(ErrorCode.LOCK_BUSY, "重复执行了哦~"))
            emitter.complete()
            return
        }

        if (PhaseState.FAILED.equalsCode(phase.phaseState)) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "该会话无法重新启动"))
            emitter.complete()
            return
        }

        if (!bizStreamMessage.connectRealtimeStreamChannel(emitter, startDto.sessionCode, startDto.phaseCode)) {
            return
        }

        if (StringUtils.isNotBlank(session.businessData)) {
            emitter.send(
                ResultVo.ok(
                    JSON.parseObject(
                        session.businessData,
                        object : TypeReference<Map<String, String>>() {})
                )
            )
        } else {
            emitter.send(ResultVo.ok<Unit>())
        }

        val saveTrackId: Long? = if (phase.phaseName == ALBUM_OUTLINE) {
            null
        } else {
            // 设置声音名称
            val track = bizAlbumTrack.getTrack(startDto.uid, startDto.sessionCode, phase.phaseName!!)
            if (track == null) {
                throw GalaxyException(ErrorCode.SESSION_ERROR, "您还没有保存专辑哦")
            } else {
                track.id
            }
        }

        try {
//            val thriftResponse = workerJobIfaceService.startSessionJob(startDto.toStartSessionJobRequest())
//
//            val response = CommonJobResponseConverter.transform(thriftResponse)
//
//            if (response.code != 200) {
//                logger.error(
//                    "启动任务失败, Session: {}, Phase: {}, Code: {}, Message: {}",
//                    startDto.sessionCode,
//                    startDto.phaseCode,
//                    response.code,
//                    response.message
//                )
//
//                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "启动会话失败")
//            }

            saveTrackId?.let {
                bizAlbumTrack.saveTrackPhaseCode(it, startDto.phaseCode)
            }

        } catch (e: Exception) {
            // 启动会话失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            val result = ResultVo.failed<Unit>(pair.first, pair.second)
            emitter.send(result)
            emitter.complete()
            bizStreamMessage.releaseStreamChannel(startDto.sessionCode, startDto.phaseCode)

            // 任务失败 删除锁
            stringRedisTemplate.delete(ongoingRedisKey)
            return
        }

    }

    override fun startSessionJob(startDto: StartSessionJobWithPhaseNameDto): Boolean {
        try {
//            val thriftResponse =
//                workerJobIfaceService.createAndStartSessionJob(startDto.toCreateAndStartPhaseJobRequest())
//
//            val response = CommonJobResponseConverter.transform(thriftResponse)
//
//            if (response.code != 200) {
//                logger.error(
//                    "启动任务失败, Session: {}, Phase: {}, Code: {}, Message: {}",
//                    startDto.sessionCode,
//                    startDto.phaseName,
//                    response.code,
//                    response.message
//                )
//
//                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "启动会话失败")
//            }


            return true
        } catch (e: Exception) {
            // 启动会话失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            throw GalaxyException(pair.first, pair.second)
        }
    }


    override fun startToolJob(emitter: SseEmitter, startDto: StartToolJobDto) {
        // 检查uid是否与sessionCode匹配
        val session = if (footballConfigBean.isAdmin(startDto.uid)) {
            sessionService.selectBySessionCode(startDto.sessionCode)
        } else {
            sessionService.selectByUidAndSessionCode(startDto.uid, startDto.sessionCode)
        }

        if (session == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话不存在"))
            emitter.complete()
            return
        }

        if (!checkSessionUid(emitter, startDto.uid, session)) {
            return
        }

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseCode(startDto.sessionCode, startDto.phaseCode)
        if (phase == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "阶段不存在"))
            emitter.complete()
            return
        }

        // 发送worker
        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(startDto.sessionCode, startDto.phaseCode)
        // 检查锁是否存在
        if (stringRedisTemplate.hasKey(ongoingRedisKey)) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", startDto.sessionCode, startDto.phaseCode)
            emitter.send(ResultVo.failed<Unit>(ErrorCode.LOCK_BUSY, "重复执行了哦~"))
            emitter.complete()
            return
        }

        if (PhaseState.FAILED.equalsCode(phase.phaseState)) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "该会话无法重新启动"))
            emitter.complete()
            return
        }

        if (!bizStreamMessage.connectRealtimeStreamChannel(emitter, startDto.sessionCode, startDto.phaseCode)) {
            return
        }

        val saveTrackId: Long? = if (phase.phaseName == ALBUM_OUTLINE) {
            null
        } else {
            // 设置声音名称
            val track = bizAlbumTrack.getTrack(startDto.uid, startDto.sessionCode, phase.phaseName!!)
            if (track == null) {
                throw GalaxyException(ErrorCode.SESSION_ERROR, "您还没有保存专辑哦")
            } else {
                track.id
            }
        }

        if (StringUtils.isNotBlank(session.businessData)) {
            emitter.send(
                ResultVo.ok(
                    JSON.parseObject(
                        session.businessData,
                        object : TypeReference<Map<String, String>>() {})
                )
            )
        } else {
            emitter.send(ResultVo.ok<Unit>())
        }

        try {
//            val thriftResponse = workerJobIfaceService.startToolJob(startDto.toStartToolJobRequest())
//
//            val response = CommonJobResponseConverter.transform(thriftResponse)
//
//            if (response.code != 200) {
//                logger.error(
//                    "启动工具失败, Session: {}, Phase: {}, Code: {}, Message: {}",
//                    startDto.sessionCode,
//                    startDto.phaseCode,
//                    response.code,
//                    response.message
//                )
//
//                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "启动会话失败")
//            }

            saveTrackId?.let {
                bizAlbumTrack.saveTrackPhaseCode(it, startDto.phaseCode)
            }

        } catch (e: Exception) {
            // 启动工具失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            val result = ResultVo.failed<Unit>(pair.first, pair.second)
            emitter.send(result)
            emitter.complete()
            bizStreamMessage.releaseStreamChannel(startDto.sessionCode, startDto.phaseCode)

            // 任务失败 删除锁
            stringRedisTemplate.delete(ongoingRedisKey)
            return
        }
    }

    override fun startToolJob(startDto: StartToolJobWithPhaseNameDto): Boolean {
        try {
//            val thriftResponse =
//                workerJobIfaceService.createAndStartToolJob(startDto.toCreateAndStartToolJobRequest())
//
//            val response = CommonJobResponseConverter.transform(thriftResponse)
//
//            if (response.code != 200) {
//                logger.error(
//                    "启动工具失败, Session: {}, Phase: {}, Tool: {}, Code: {}, Message: {}",
//                    startDto.sessionCode,
//                    startDto.phaseName,
//                    startDto.toolId,
//                    response.code,
//                    response.message
//                )
//
//                throw GalaxyException(ErrorCode.SESSION_ERROR, response.message ?: "启动会话失败")
//            }


            return true
        } catch (e: Exception) {
            // 启动会话失败
            val pair = if (e is GalaxyException) {
                Pair(ErrorCode.SESSION_ERROR, e.message)
            } else {
                Pair(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString())
            }

            throw GalaxyException(pair.first, pair.second)
        }
    }

    override fun updateBlockContent(sessionCode: Long, phaseCode: Long, blockCode: Long, content: String): Boolean {
        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode)
        // 检查锁是否存在
        GalaxyAsserts.assertFalse(
            stringRedisTemplate.hasKey(ongoingRedisKey),
            ErrorCode.BLOCK_ERROR,
            "正在执行中请稍后哦~"
        )

        return blockService.updateBlock(sessionCode, phaseCode, blockCode, content)
    }

    override fun getHistoricalSessions(uid: Long, pageRequest: QueryPageRequestVo): GalaxyPage<SessionVo> {
        val page = sessionService.ktQuery()
            .eq(GalaxySessionEntity::uid, uid)
            .eq(GalaxySessionEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(GalaxySessionEntity::createTime)
            .page(pageRequest.toPageInfo())

        return GalaxyPage.of(page).map { SessionVo.of(it) }
    }

    @Transactional(rollbackFor = [Throwable::class])
    override fun removeSession(uid: Long, sessionCode: Long): Boolean {
        // 必须先删专辑
        val album = albumService.ktQuery()
            .eq(AlbumEntity::sessionCode, sessionCode)
            .eq(AlbumEntity::uid, uid)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
        GalaxyAsserts.assertNull(album, ErrorCode.ALBUM_ERROR, "必须删除专辑后才能删除会话哦~")

        val session = sessionService.selectByUidAndSessionCode(uid, sessionCode) ?: return true
        GalaxyAsserts.assertEquals(uid, session.uid, ErrorCode.SESSION_ERROR, "这不是您的会话哦~")

        val now = LocalDateTime.now()

        GalaxyAsserts.assertTrue(
            sessionService.ktUpdate()
                .eq(GalaxySessionEntity::sessionCode, sessionCode)
                .set(GalaxySessionEntity::deletedAt, LogicDeleted.DELETED.getCode())
                .set(GalaxySessionEntity::updateTime, now)
                .update(), ErrorCode.SESSION_ERROR, "删除会话失败"
        )

        GalaxyAsserts.assertTrue(
            phaseService.ktUpdate()
                .eq(GalaxyPhaseEntity::sessionCode, sessionCode)
                .set(GalaxyPhaseEntity::deletedAt, LogicDeleted.DELETED.getCode())
                .set(GalaxyPhaseEntity::updateTime, now)
                .update(), ErrorCode.SESSION_ERROR, "删除阶段失败"
        )

        GalaxyAsserts.assertTrue(
            blockService.ktUpdate()
                .eq(GalaxyBlockEntity::sessionCode, sessionCode)
                .set(GalaxyBlockEntity::deletedAt, LogicDeleted.DELETED.getCode())
                .set(GalaxyBlockEntity::updateTime, now)
                .update(), ErrorCode.SESSION_ERROR, "删除数据失败"
        )

        return true
    }

    override fun checkSessionUid(uid: Long, session: GalaxySessionEntity) {
        // 管理员不判断
        if (!footballConfigBean.isAdmin(uid)) {
            GalaxyAsserts.assertEquals(session.uid, uid, ErrorCode.SESSION_ERROR, "这里不是您的会话哦~")
        }
    }

    override fun checkSessionUid(emitter: SseEmitter, uid: Long, session: GalaxySessionEntity): Boolean {
        // 管理员不判断
        if (footballConfigBean.isAdmin(uid)) {
            return true
        }

        if (session.uid != uid) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "这里不是您的会话哦~"))
            emitter.complete()
            return false
        }

        return true
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }

}