package com.ximalaya.galaxy.business.business.boss.vo

/**
 *<AUTHOR>
 *@create 2025-05-20 16:02
 */
data class CreateSessionJobDto(

    var uid: Long,

    var phaseName: String,

    var systemPrompt: String,

    var prompt: String,

    var businessData: Map<String, String>? = null
) {

//    fun toCreateSessionJobRequest(): CreateSessionJobRequest {
//        return CreateSessionJobRequest.Builder()
//            .setUid(this.uid)
//            .setPhaseName(this.phaseName)
//            .setSystemPrompt(this.systemPrompt)
//            .setPrompt(this.prompt)
//            .setBusinessData(JSON.toJSONString(this.businessData))
//            .build()
//    }

}