package com.ximalaya.galaxy.business.business.boss.vo

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.PhaseState

/**
 *<AUTHOR>
 *@create 2025-05-22 21:55
 */
class SessionPhasesVo {

    var sessionCode: String? = null

    var sessionName: String? = null

    var businessData: Map<String, String>? = null

    var uid: Long? = null

    var phases: List<PhaseVo>? = null

    companion object {

        fun of(sessionEntity: GalaxySessionEntity, phaseEntities: List<GalaxyPhaseEntity>?): SessionPhasesVo {
            return SessionPhasesVo().apply {
                this.sessionCode = sessionEntity.sessionCode.toString()
                this.sessionName = sessionEntity.sessionName
                this.businessData = sessionEntity.businessData?.let {
                    JSON.parseObject(
                        it,
                        object : TypeReference<Map<String, String>>() {})
                }
                this.uid = sessionEntity.uid
                this.phases = phaseEntities?.map { PhaseVo.of(it) }
            }
        }

    }

}

class PhaseVo {

    var phaseCode: String? = null

    var phaseName: String? = null

    var phaseState: PhaseState? = null

    companion object {

        fun of(phaseEntity: GalaxyPhaseEntity): PhaseVo {
            return PhaseVo().apply {
                this.phaseCode = phaseEntity.phaseCode.toString()
                this.phaseName = phaseEntity.phaseName
                this.phaseState = PhaseState.parseCode(phaseEntity.phaseState)
            }
        }

    }

}