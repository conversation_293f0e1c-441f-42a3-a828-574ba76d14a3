package com.ximalaya.galaxy.business.business.boss.impl

import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.boss.BizStreamMessage
import com.ximalaya.galaxy.business.business.boss.sse.SseCloseSender
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.support.getAgentPendingMessageCacheKey
import com.ximalaya.galaxy.business.service.support.getAgentStopKey
import com.ximalaya.galaxy.business.service.vo.*
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RedissonClient
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock

/**
 *<AUTHOR>
 *@create 2025-06-05 21:32
 */
@BusinessComponent
class BizStreamMessageImpl(
    private val sseCloseSender: SseCloseSender,
    private val blockService: GalaxyBlockService,
    private val stringRedisTemplate: StringRedisTemplate,
    private val redissonClient: RedissonClient,
) : BizStreamMessage {

    private var sseMap = ConcurrentHashMap<Pair<Long, Long>, SseResource>()

    override fun reconnectStreamChannel(
        emitter: SseEmitter,
        sessionCode: Long,
        phaseCode: Long
    ): Boolean {
        return doConnectStreamChannel(emitter, sessionCode, phaseCode, false)
    }

    override fun connectRealtimeStreamChannel(emitter: SseEmitter, sessionCode: Long, phaseCode: Long): Boolean {
        return doConnectStreamChannel(emitter, sessionCode, phaseCode, true)
    }

    private fun doConnectStreamChannel(
        emitter: SseEmitter,
        sessionCode: Long,
        phaseCode: Long,
        historyCompleted: Boolean
    ): Boolean {
        val connectRedisKey = RedisLockHelper.getPhaseConnectLockKey(sessionCode, phaseCode)
        // 设置有效期为 20 分钟
        var isLockAcquired = stringRedisTemplate.opsForValue()
            .setIfAbsent(connectRedisKey, phaseCode.toString(), 20, TimeUnit.MINUTES)
        if (isLockAcquired != true) {
            logger.warn(
                "First Check 尝试停止旧的流, 3s后将继续尝试, Session: {}, Phase: {}",
                sessionCode,
                phaseCode
            )

            // 尝试停止旧的流
            sseCloseSender.sendClose(sessionCode, phaseCode)
            TimeUnit.SECONDS.sleep(3)

            // double check
            isLockAcquired = stringRedisTemplate.opsForValue()
                .setIfAbsent(connectRedisKey, phaseCode.toString(), 20, TimeUnit.MINUTES)
            if (isLockAcquired != true) {
                logger.warn("Double Check 不允许同时打开多个窗口, Session: {}, Phase: {}", sessionCode, phaseCode)

                emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "数据通道繁忙，请重试"))
                emitter.complete()
                return false
            }
        }

        // 初始状态：emitter已连接，但历史消息尚未完成发送
        sseMap[Pair(sessionCode, phaseCode)] = SseResource(emitter, historyCompleted)
        return true
    }

    override fun releaseStreamChannel(sessionCode: Long, phaseCode: Long) {
        sseMap.remove(Pair(sessionCode, phaseCode))
        stringRedisTemplate.delete(RedisLockHelper.getPhaseConnectLockKey(sessionCode, phaseCode))
        // 清理待处理消息队列
        stringRedisTemplate.delete(getAgentPendingMessageCacheKey(sessionCode, phaseCode))
    }

    override fun sendSseWriteBackMessage(agentMessage: AgentContentProtocol) {
        val sessionCode = agentMessage._sessionCode ?: return
        val phaseCode = agentMessage._phaseCode ?: return
        val sessionPhasePair = agentMessage.toPair()

        val sseResource = sseMap[sessionPhasePair] ?: return
        val (_, historyCompleted) = sseResource

        // 将当前消息添加到队列
        appendAgentBufferMessage(sessionCode, phaseCode, agentMessage)

        // 如果历史消息已处理完成
        if (historyCompleted) {
            // 处理队列中的消息
            processAgentBufferMessages(sessionCode, phaseCode)
            return
        }

        // 如果历史消息尚未处理完成
        // 尝试获取Redis锁来处理历史消息
        try {
            if (!sseResource.historyLock.tryLock()) {
                // 如果无法获取锁，说明其他线程正在处理历史消息
                return
            }

            // double check 如果已经被其他线程处理完成，直接返回
            if (sseMap[sessionPhasePair]?.historyCompleted == true) {
                // 处理队列中的消息
                processAgentBufferMessages(sessionCode, phaseCode)
                return
            }

            // 处理历史消息
            val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
            val listSize = stringRedisTemplate.opsForList().size(messageKey) ?: 0

            if (listSize > 0) {
                // 获取Redis list中的所有消息并发送
                val messages = stringRedisTemplate.opsForList().range(messageKey, 0, listSize - 1)
                if (CollectionUtils.isNotEmpty(messages)) {
                    for (messageJson in messages!!) {
                        val message = AgentContentProtocol.parseJson(messageJson)
                        if (sendStream(sessionCode, phaseCode, message)) {
                            return
                        }
                    }
                }
            } else {
                // 从db查询历史消息 证明流已经结束了
                val blocks = blockService.ktQuery()
                    .eq(GalaxyBlockEntity::sessionCode, sessionCode)
                    .eq(GalaxyBlockEntity::phaseCode, phaseCode)
                    .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
                    .list()

                if (CollectionUtils.isEmpty(blocks)) {
                    // 没有找到数据块 报错
                    logger.error("未找到会话数据块, Session: {}, Phase: {}", sessionCode, phaseCode)
                    sendAndStopStream(
                        sessionCode, phaseCode, ResultVo.failed<Unit>(
                            GalaxyException(
                                ErrorCode.SESSION_ERROR,
                                "数据不存在！！！"
                            )
                        )
                    )
                    return
                }
                // 发送数据块后停止
                emitPhaseMessageFormDB(sessionCode, phaseCode, blocks)
                sendAndStopStream(sessionCode, phaseCode, ResultVo.ok(AgentContentFinish().apply {
                    this._sessionCode = sessionCode
                    this._phaseCode = phaseCode
                }))
                return
            }

            // 标记历史消息已完成发送
            sseMap[sessionPhasePair]?.let {
                it.historyCompleted = true
            }

            // 处理队列中的消息
            processAgentBufferMessages(sessionCode, phaseCode)

        } catch (e: InterruptedException) {
            logger.error("BizStreamMessage:sendSseWriteBackMessage InterruptedException", e)

            Thread.currentThread().interrupt()
            throw GalaxyException(ErrorCode.THREAD_INTERRUPTED)
        } finally {
            if (sseResource.historyLock.isLocked && sseResource.historyLock.isHeldByCurrentThread) {
                sseResource.historyLock.unlock()
            }
        }

    }

    override fun stop(sessionCode: Long, phaseCode: Long) {
        val stopAgentKey = getAgentStopKey(sessionCode, phaseCode)
        val stopAgentBucket = redissonClient.getBucket<String>(stopAgentKey)
        if (stopAgentBucket.isExists) {
            // 正在停止中
            return
        }

        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode)
        // 检查是否在进行中
        if (stringRedisTemplate.hasKey(ongoingRedisKey)) {
            stopAgentBucket.set("stop", 1, TimeUnit.MINUTES)
            return
        }
    }

    fun emitPhaseMessageFormDB(
        sessionCode: Long,
        phaseCode: Long,
        blocks: List<GalaxyBlockEntity>
    ) {
        var index = 0L
        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.SYSTEM_PROMPT -> {
                    // 系统提示词不需要出现在上下文
                    continue
                }

                BlockType.USER -> {
                    val message = AgentContentBlockUser().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }


                BlockType.TEXT -> {
                    val blockMessage = AgentContentBlockText().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, blockMessage)) {
                        return
                    }

                    val message = AgentContentText().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.text = block.content
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.MCP_TOOL_CALL -> {
                    val blockMessage = AgentContentBlockToolCall().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, blockMessage)) {
                        return
                    }

                    val message = AgentContentProtocol.parseJson(block.content!!).apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.MCP_TOOL_RESULT -> {
                    val blockMessage = AgentContentBlockToolResult().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, blockMessage)) {
                        return
                    }

                    val message = AgentContentProtocol.parseJson(block.content!!).apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.DIFY_CALL -> {
                    val message = AgentContentBlockDifyCall().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.args = DifyRequestVo.parseJson(block.content!!)
                    }

                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.DIFY_RESULT -> {
                    val message = AgentContentBlockDifyResult().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.result = DifyResponseVo.parseJson(block.content!!)
                    }

                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.ERROR, BlockType.EXCEPTION -> {
                    val message = AgentContentProtocol.parseJson(block.content!!).apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }

                    break
                }
            }

        }
    }

    private fun appendAgentBufferMessage(sessionCode: Long, phaseCode: Long, agentMessage: AgentContentProtocol) {
        val pendingKey = getAgentPendingMessageCacheKey(sessionCode, phaseCode)
        stringRedisTemplate.opsForList().rightPush(pendingKey, agentMessage.toJson())
        stringRedisTemplate.expire(pendingKey, 20, TimeUnit.MINUTES)
    }

    // 处理队列中的消息
    private fun processAgentBufferMessages(sessionCode: Long, phaseCode: Long) {
        val sseResource = sseMap[Pair(sessionCode, phaseCode)] ?: return
        val pendingMessageKey = getAgentPendingMessageCacheKey(sessionCode, phaseCode)

        try {
            if (!sseResource.pendingLock.tryLock()) {
                // 等300ms后再重试一次
                TimeUnit.MILLISECONDS.sleep(300)
                // Double Check 防止 A线程准备释放锁时 B线程获取不到锁
                if (!sseResource.pendingLock.tryLock()) {
                    return
                }
            }
            // 循环处理队列中的所有消息
            while (true) {
                val pendingJson = stringRedisTemplate.opsForList().leftPop(pendingMessageKey)
                if (StringUtils.isBlank(pendingJson)) {
                    break
                }
                val pendingMessage = AgentContentProtocol.parseJson(pendingJson!!)

                if (sendStream(sessionCode, phaseCode, pendingMessage)) {
                    break
                }

            }
        } catch (e: InterruptedException) {
            logger.error("BizStreamMessage:processAgentPendingMessageMessages InterruptedException", e)

            Thread.currentThread().interrupt()
            throw GalaxyException(ErrorCode.THREAD_INTERRUPTED)
        } finally {
            if (sseResource.pendingLock.isLocked && sseResource.pendingLock.isHeldByCurrentThread) {
                sseResource.pendingLock.unlock()
            }
        }
    }

    /**
     * 返回是否需要停止
     */
    private fun sendStream(sessionCode: Long, phaseCode: Long, agentMessage: AgentContentProtocol): Boolean {
        val (emitter, _) = sseMap[Pair(sessionCode, phaseCode)] ?: return true
        try {
            emitter.send(ResultVo.ok(agentMessage))

            // 如果是结束消息，完成发送并释放资源
            if (agentMessage._type in setOf(
                    AgentContentType.FINISH,
                    AgentContentType.ERROR,
                    AgentContentType.EXCEPTION
                )
            ) {
                releaseStreamChannel(sessionCode, phaseCode)
                emitter.complete()

                return true
            }

            return false

        } catch (e: Exception) {
            logger.error("BizStreamMessage:doSend Exception", e)

            releaseStreamChannel(sessionCode, phaseCode)
            emitter.complete()

            return true
        }
    }

    override fun <T> sendAndStopStream(sessionCode: Long, phaseCode: Long, resultVo: ResultVo<T>?) {
        val (emitter, _) = sseMap[Pair(sessionCode, phaseCode)] ?: return
        try {
            resultVo?.let { emitter.send(it) }

            releaseStreamChannel(sessionCode, phaseCode)
            emitter.complete()
        } catch (e: Exception) {
            logger.error("BizStreamMessage:doSendAndStop Exception", e)
            releaseStreamChannel(sessionCode, phaseCode)
            emitter.complete()
        }
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }

}

class SseResource(
    val emitter: SseEmitter,
    var historyCompleted: Boolean,
) {

    val historyLock = ReentrantLock()
    val pendingLock = ReentrantLock()

    operator fun component1(): SseEmitter = emitter

    operator fun component2(): Boolean = historyCompleted

}