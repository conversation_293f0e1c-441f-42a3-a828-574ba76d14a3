package com.ximalaya.galaxy.business.business.boss.vo

/**
 *<AUTHOR>
 *@create 2025-05-20 16:02
 */
data class StartSessionJobWithPhaseNameDto(

  var uid: Long,

  var sessionCode: Long,

  var phaseName: String,

  var systemPromptTemplate: String,

  var prompt: String,
) {

//  fun toCreateAndStartPhaseJobRequest(): CreateAndStartPhaseJobRequest {
//    // 注入sessionCode
//    val systemPrompt = PromptTemplate(systemPromptTemplate).render(mapOf("sessionCode" to sessionCode))
//
//    return CreateAndStartPhaseJobRequest.Builder()
//      .setUid(this.uid)
//      .setSessionCode(this.sessionCode)
//      .setPhaseName(this.phaseName)
//      .setSystemPrompt(systemPrompt)
//      .setPrompt(this.prompt)
//      .build()
//  }

}