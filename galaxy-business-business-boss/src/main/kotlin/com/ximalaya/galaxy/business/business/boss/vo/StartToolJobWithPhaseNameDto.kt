package com.ximalaya.galaxy.business.business.boss.vo

/**
 *<AUTHOR>
 *@create 2025-05-28 17:26
 */
data class StartToolJobWithPhaseNameDto(

    var uid: Long,

    var sessionCode: Long,

    var phaseName: String,

    var toolId: Long,

    var args: Map<String, String?>? = null,

    var needRemoveBlockCodes: List<Long>? = null,
) {

//    fun toCreateAndStartToolJobRequest(): CreateAndStartToolJobRequest {
//        return CreateAndStartToolJobRequest.Builder()
//            .setUid(this.uid)
//            .setSessionCode(this.sessionCode)
//            .setPhaseName(this.phaseName)
//            .setToolId(this.toolId)
//            .setArgs(JSON.toJSONString(this.args))
//            .setNeedRemoveBlockCodes(needRemoveBlockCodes)
//            .build()
//    }

}