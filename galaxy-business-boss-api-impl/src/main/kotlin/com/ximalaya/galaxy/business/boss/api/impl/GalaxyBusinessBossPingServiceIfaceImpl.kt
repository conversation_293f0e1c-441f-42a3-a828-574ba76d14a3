package com.ximalaya.galaxy.business.boss.api.impl

import com.ximalaya.eros.mainstay.context.annotation.MainstayServer
import com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest
import com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse
import com.ximalaya.galaxy.business.boss.api.converter.PingRequestConverter
import com.ximalaya.galaxy.business.boss.api.thrift.GalaxyBusinessBossPingService
import com.ximalaya.mainstay.rpc.thrift.TException

/**
 *<AUTHOR>
 *@create 2024-11-28 11:58
 */
@MainstayServer(group = "galaxy-business-boss")
open class GalaxyBusinessBossPingServiceIfaceImpl : GalaxyBusinessBossPingService.Iface {

  @Throws(TException::class)
  override fun ping(pingRequest: PingRequest?): PongResponse? {
    val ping = PingRequestConverter.transform(pingRequest)

    val message = ping.message ?: "Galaxy Boss"

    return PongResponse.Builder()
      .setMessage("Hello $message")
      .build()
  }

}