package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import java.time.LocalDateTime

/**
 * 用户登录日志实体类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@TableName("gxy_user_login_log")
data class UserLoginLogEntity(
    var id: Long? = null,
    
    var userId: Long? = null,
    
    var loginTime: LocalDateTime? = null,
    
    var loginIp: String? = null,
    
    var deviceInfo: String? = null,
    
    var userAgent: String? = null,
    
    var status: Int? = null,
    
    var failReason: String? = null,
    
    var createTime: LocalDateTime? = null,
) {
    companion object {
        fun of(
            userId: Long,
            loginTime: LocalDateTime,
            loginIp: String? = null,
            deviceInfo: String? = null,
            userAgent: String? = null,
            status: Int,
            failReason: String? = null
        ): UserLoginLogEntity {
            val now = LocalDateTime.now()
            return UserLoginLogEntity().apply {
                this.userId = userId
                this.loginTime = loginTime
                this.loginIp = loginIp
                this.deviceInfo = deviceInfo
                this.userAgent = userAgent
                this.status = status
                this.failReason = failReason
                this.createTime = now
            }
        }
    }
} 