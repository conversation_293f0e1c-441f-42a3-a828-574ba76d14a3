package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.GalaxyPhaseMapper
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
@Repository
class GalaxyPhaseServiceImpl : ServiceImpl<GalaxyPhaseMapper, GalaxyPhaseEntity>(), GalaxyPhaseService {

    override fun selectBySessionCode(sessionCode: Long): List<GalaxyPhaseEntity> {
        return ktQuery().eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .eq(GalaxyPhaseEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()
    }

    override fun selectByPhaseCodes(phaseCodes: Collection<Long>): List<GalaxyPhaseEntity> {
        if (CollectionUtils.isEmpty(phaseCodes)) {
            return emptyList()
        }

        return ktQuery().`in`(GalaxyPhaseEntity::phaseCode, phaseCodes)
            .eq(GalaxyPhaseEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()
    }

    override fun selectBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): GalaxyPhaseEntity? {
        return ktQuery().eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .eq(GalaxyPhaseEntity::phaseCode, phaseCode)
            .eq(GalaxyPhaseEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
    }

    override fun selectBySessionCodeAndPhaseName(sessionCode: Long, phaseName: String): GalaxyPhaseEntity? {
        return ktQuery().eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .eq(GalaxyPhaseEntity::phaseName, phaseName)
            .eq(GalaxyPhaseEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
    }

    override fun deleteBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): Boolean {
        return ktUpdate().eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .eq(GalaxyPhaseEntity::phaseCode, phaseCode)
            .set(GalaxyPhaseEntity::deletedAt, LogicDeleted.DELETED.getCode())
            .set(GalaxyPhaseEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun deleteNotOutlineBySessionCode(sessionCode: Long): Boolean {
        return ktUpdate().eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .ne(GalaxyPhaseEntity::phaseName, ALBUM_OUTLINE)
            .set(GalaxyPhaseEntity::deletedAt, LogicDeleted.DELETED.getCode())
            .set(GalaxyPhaseEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun deleteNotOutlineBySessionCodeAndPhaseName(sessionCode: Long, phaseName: String): Boolean {
        return ktUpdate().eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .eq(GalaxyPhaseEntity::phaseName, phaseName)
            .set(GalaxyPhaseEntity::deletedAt, LogicDeleted.DELETED.getCode())
            .set(GalaxyPhaseEntity::updateTime, LocalDateTime.now())
            .update()
    }

}