package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.InvitationCodeEntity
import com.ximalaya.galaxy.business.repo.enums.InvitationCodeState
import com.ximalaya.galaxy.business.repo.mapper.InvitationCodeMapper
import com.ximalaya.galaxy.business.repo.service.InvitationCodeService
import org.springframework.stereotype.Repository

/**
 *<AUTHOR>
 *@create 2023-08-29 15:18
 */
@Repository
class InvitationCodeServiceImpl : InvitationCodeService, ServiceImpl<InvitationCodeMapper, InvitationCodeEntity>() {

  override fun selectByUid(uid: Long): InvitationCodeEntity? {
    return ktQuery().eq(InvitationCodeEntity::uid, uid)
      .eq(InvitationCodeEntity::codeState, InvitationCodeState.USED.code)
      .one()
  }

}