package com.ximalaya.galaxy.business.repo.enums

import com.ximalaya.galaxy.business.common.enums.Enums

/**
 *<AUTHOR>
 *@create 2023-08-29 15:10
 */
enum class InvitationCodeState(
  val code: Int,
  val desc: String
) {

  UNUSED(0, "未使用"),
  USED(1, "已使用"),
  ;

  companion object {

    @JvmStatic
    fun parseCode(code: Int?): InvitationCodeState {
      return Enums.parseNotNull(
        values(),
        InvitationCodeState::code,
        code,
        "Enum not support InvitationCodeState: $code"
      )
    }

  }

}