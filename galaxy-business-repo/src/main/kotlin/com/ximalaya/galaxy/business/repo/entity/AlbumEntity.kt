package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 10:00
 */
@TableName("gxy_album")
data class AlbumEntity(
    var id: Long? = null,

    var uid: Long? = null,

    var sessionCode: Long? = null,

    var albumName: String? = null,

    var deletedAt: Int? = null,

    var createTime: LocalDateTime? = null,
    var updateTime: LocalDateTime? = null,

    var dataCreateTime: LocalDateTime? = null,
    var dataUpdateTime: LocalDateTime? = null,
)