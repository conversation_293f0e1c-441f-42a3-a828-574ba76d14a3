package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.TrackEntity

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
interface TrackService : IService<TrackEntity> {

    fun selectBySessionCode(sessionCode: Long): List<TrackEntity>

    fun selectBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): TrackEntity?

    fun createTrack(
        albumId: Long,
        uid: Long,
        sessionCode: Long,
        phaseCode: Long,
        trackName: String?,
        trackContent: String?,
    ): Boolean

    fun updateTrack(id: Long, trackName: String?, trackContent: String?): Boolean

    fun deleteBySessionCode(sessionCode: Long): Boolean

    fun deleteBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): Boolean

    fun deleteTrack(id: Long): Boolean

}
