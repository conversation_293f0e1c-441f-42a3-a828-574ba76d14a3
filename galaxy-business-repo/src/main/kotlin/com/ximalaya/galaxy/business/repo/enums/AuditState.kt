package com.ximalaya.galaxy.business.repo.enums

import com.ximalaya.galaxy.business.common.enums.Enums

/**
 *<AUTHOR>
 *@create 2024-11-29 15:24
 */
enum class AuditState(
  val code: Int,
  val desc: String
) {

  INIT(0, "未送审"),

  SUCCESS(1, "审核通过"),

  FAILED(-1, "审核不通过"),
  ;

  fun equalsCode(dbCode: Int?) =
    when (dbCode) {
      null -> false
      else -> code == dbCode
    }

  companion object {

    @JvmStatic
    fun parseCode(code: Int?): AuditState {
      return Enums.parseNotNull(
        values(),
        AuditState::code,
        code,
        "not support AuditState: $code"
      )
    }

  }

}