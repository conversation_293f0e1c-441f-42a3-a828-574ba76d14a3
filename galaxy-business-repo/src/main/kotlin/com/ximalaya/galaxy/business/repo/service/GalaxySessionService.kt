package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
interface GalaxySessionService : IService<GalaxySessionEntity> {

  fun selectBySessionCode(sessionCode: Long): GalaxySessionEntity?

  fun selectByUidAndSessionCode(uid: Long, sessionCode: Long): GalaxySessionEntity?

}