package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.TrackEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.TrackMapper
import com.ximalaya.galaxy.business.repo.service.TrackService
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
@Repository
class TrackServiceImpl : ServiceImpl<TrackMapper, TrackEntity>(), TrackService {

    override fun selectBySessionCode(sessionCode: Long): List<TrackEntity> {
        return ktQuery().eq(TrackEntity::sessionCode, sessionCode)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()
    }

    override fun selectBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): TrackEntity? {
        return ktQuery().eq(TrackEntity::phaseCode, phaseCode)
            .eq(TrackEntity::sessionCode, sessionCode)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
    }

    override fun createTrack(
        albumId: Long,
        uid: Long,
        sessionCode: Long,
        phaseCode: Long,
        trackName: String?,
        trackContent: String?
    ): Boolean {
        val now = LocalDateTime.now()

        val trackEntity = TrackEntity().apply {
            this.albumId = albumId
            this.uid = uid
            this.sessionCode = sessionCode
            this.phaseCode = phaseCode
            this.trackName = trackName
            this.trackContent = trackContent
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
        }

        return save(trackEntity)
    }

    override fun updateTrack(id: Long, trackName: String?, trackContent: String?): Boolean {
        val now = LocalDateTime.now()

        val trackEntity = TrackEntity().apply {
            this.id = id
            this.updateTime = now
            if (StringUtils.isNotBlank(trackName)) {
                this.trackName = trackName
            }
            if (trackContent != null) {
                this.trackContent = trackContent
            }
        }

        return updateById(trackEntity)
    }

    override fun deleteBySessionCode(sessionCode: Long): Boolean {
        return ktUpdate().eq(TrackEntity::sessionCode, sessionCode)
            .set(TrackEntity::deletedAt, LogicDeleted.DELETED.getCode())
            .set(TrackEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun deleteBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): Boolean {
        return ktUpdate().eq(TrackEntity::sessionCode, sessionCode)
            .eq(TrackEntity::phaseCode, phaseCode)
            .set(TrackEntity::deletedAt, LogicDeleted.DELETED.getCode())
            .set(TrackEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun deleteTrack(id: Long): Boolean {
        val now = LocalDateTime.now()

        val trackEntity = TrackEntity().apply {
            this.id = id
            this.deletedAt = LogicDeleted.DELETED.getCode()
            this.updateTime = now
        }

        return updateById(trackEntity)
    }

}
