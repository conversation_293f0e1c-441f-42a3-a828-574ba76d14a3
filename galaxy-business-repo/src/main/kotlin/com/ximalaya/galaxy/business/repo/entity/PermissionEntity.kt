package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import java.time.LocalDateTime

/**
 * 权限实体类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@TableName("gxy_permission")
data class PermissionEntity(
    var id: Int? = null,
    
    var name: String? = null,
    
    var description: String? = null,
    
    var status: Int? = null,
    
    var createTime: LocalDateTime? = null,
    
    var updateTime: LocalDateTime? = null,
) {
    companion object {
        fun of(
            name: String,
            description: String? = null
        ): PermissionEntity {
            val now = LocalDateTime.now()
            return PermissionEntity().apply {
                this.name = name
                this.description = description
                this.status = 1
                this.createTime = now
                this.updateTime = now
            }
        }
    }
} 