package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import java.time.LocalDateTime

/**
 * 用户Token实体类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@TableName("gxy_user_token")
data class UserTokenEntity(
    var id: Long? = null,
    
    var userId: Long? = null,
    
    var token: String? = null,
    
    var refreshToken: String? = null,
    
    var expireTime: LocalDateTime? = null,
    
    var refreshExpireTime: LocalDateTime? = null,
    
    var deviceInfo: String? = null,
    
    var ipAddress: String? = null,
    
    var status: Int? = null,
    
    var createTime: LocalDateTime? = null,
    
    var updateTime: LocalDateTime? = null,
    
    var dataCreateTime: LocalDateTime? = null,
    
    var dataUpdateTime: LocalDateTime? = null,
) {
    companion object {
        fun of(
            userId: Long,
            token: String,
            refreshToken: String? = null,
            expireTime: LocalDateTime,
            refreshExpireTime: LocalDateTime? = null,
            deviceInfo: String? = null,
            ipAddress: String? = null
        ): UserTokenEntity {
            val now = LocalDateTime.now()
            return UserTokenEntity().apply {
                this.userId = userId
                this.token = token
                this.refreshToken = refreshToken
                this.expireTime = expireTime
                this.refreshExpireTime = refreshExpireTime
                this.deviceInfo = deviceInfo
                this.ipAddress = ipAddress
                this.status = 1
                this.createTime = now
                this.updateTime = now
            }
        }
    }
} 