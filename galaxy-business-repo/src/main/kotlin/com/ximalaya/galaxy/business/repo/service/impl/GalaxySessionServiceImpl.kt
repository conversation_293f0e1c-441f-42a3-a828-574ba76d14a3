package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.GalaxySessionMapper
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import org.springframework.stereotype.Repository

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
@Repository
class GalaxySessionServiceImpl : ServiceImpl<GalaxySessionMapper, GalaxySessionEntity>(), GalaxySessionService {

  override fun selectBySessionCode(sessionCode: Long): GalaxySessionEntity? {
    return ktQuery().eq(GalaxySessionEntity::sessionCode, sessionCode)
      .eq(GalaxySessionEntity::deletedAt, LogicDeleted.SAVE.getCode())
      .one()
  }

  override fun selectByUidAndSessionCode(uid: Long, sessionCode: Long): GalaxySessionEntity? {
    return ktQuery().eq(GalaxySessionEntity::uid, uid)
      .eq(GalaxySessionEntity::sessionCode, sessionCode)
      .eq(GalaxySessionEntity::deletedAt, LogicDeleted.SAVE.getCode())
      .one()
  }

}