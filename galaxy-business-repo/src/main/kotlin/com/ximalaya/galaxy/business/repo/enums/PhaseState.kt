package com.ximalaya.galaxy.business.repo.enums

import com.ximalaya.galaxy.business.common.enums.Enums

/**
 *<AUTHOR>
 *@create 2025-05-14 14:33
 */
enum class PhaseState(
  val code: Int,
  val desc: String
) {

  INIT(0, "初始化"),
  RUNNING(1, "进行中"),
  FINISHED(2, "已结束"),
  FAILED(-1, "失败"),
  ;

  fun equalsCode(dbCode: Int?) =
    when (dbCode) {
      null -> false
      else -> code == dbCode
    }

  companion object {

    @JvmStatic
    fun parseCode(code: Int?): PhaseState {
      return Enums.parseNotNull(
        PhaseState.values(),
        PhaseState::code,
        code,
        "Enum not support PhaseState: $code"
      )
    }

  }
}