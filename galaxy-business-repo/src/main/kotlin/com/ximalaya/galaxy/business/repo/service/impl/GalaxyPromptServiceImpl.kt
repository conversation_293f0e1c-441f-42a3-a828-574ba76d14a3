package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.GalaxyPromptEntity
import com.ximalaya.galaxy.business.repo.mapper.GalaxyPromptMapper
import com.ximalaya.galaxy.business.repo.service.GalaxyPromptService
import org.springframework.stereotype.Repository

/**
 *<AUTHOR>
 *@create 2025-05-20 14:57
 */
@Repository
class GalaxyPromptServiceImpl : ServiceImpl<GalaxyPromptMapper, GalaxyPromptEntity>(), GalaxyPromptService {

  override fun selectByName(name: String): GalaxyPromptEntity? {
    return ktQuery().eq(GalaxyPromptEntity::name, name)
      .one()
  }

}