package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import java.time.LocalDateTime

/**
 * 用户实体类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@TableName("gxy_user")
data class UserEntity(
    var id: Long? = null,
    
    var username: String? = null,
    
    var password: String? = null,
    
    var nickname: String? = null,
    
    var email: String? = null,
    
    var phone: String? = null,
    
    var avatar: String? = null,
    
    var status: Int? = null,
    
    var permissionId: Int? = null,
    var invitationCode: String? = null,
    var inviterId: Long? = null,
    
    var lastLoginTime: LocalDateTime? = null,
    
    var lastLoginIp: String? = null,
    
    var createTime: LocalDateTime? = null,
    
    var updateTime: LocalDateTime? = null,
    
    var dataCreateTime: LocalDateTime? = null,
    
    var dataUpdateTime: LocalDateTime? = null,
) {
    companion object {
        fun of(
            username: String,
            password: String,
            nickname: String? = null,
            email: String? = null,
            phone: String? = null,
            permissionId: Int = 0,
            invitationCode: String? = null,
            inviterId: Long? = null
        ): UserEntity {
            val now = LocalDateTime.now()
            return UserEntity().apply {
                this.username = username
                this.password = password
                this.nickname = nickname
                this.email = email
                this.phone = phone
                this.permissionId = permissionId
                this.invitationCode = invitationCode
                this.inviterId = inviterId
                this.status = 1
                this.createTime = now
                this.updateTime = now
            }
        }
    }
} 