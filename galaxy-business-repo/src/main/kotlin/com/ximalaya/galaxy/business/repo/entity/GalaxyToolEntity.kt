package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-28 10:39
 */
@TableName("gxy_tool")
data class GalaxyToolEntity(
  var id: Long? = null,

  var toolName: String? = null,

  /**
   * @see com.ximalaya.galaxy.business.repo.enums.GalaxyToolType
   */
  var toolType: Int? = null,

  /**
   * @see com.ximalaya.galaxy.business.repo.dto.MetadataDto
   */
  var metadata: String? = null,

  var createTime: LocalDateTime? = null,
  var updateTime: LocalDateTime? = null,

  var dataCreateTime: LocalDateTime? = null,
  var dataUpdateTime: LocalDateTime? = null,
)