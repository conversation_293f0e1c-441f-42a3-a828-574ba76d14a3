package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import com.ximalaya.galaxy.business.repo.support.GALAXY_SESSION_TABLE_NAME
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-14 14:17
 */
@TableName(GALAXY_SESSION_TABLE_NAME)
data class GalaxySessionEntity(
  var id: Long? = null,

  var uid: Long? = null,

  var sessionCode: Long? = null,

  var sessionName: String? = null,

  var businessData: String? = null,

  var deletedAt: Int? = null,

  var createTime: LocalDateTime? = null,
  var updateTime: LocalDateTime? = null,

  var dataCreateTime: LocalDateTime? = null,
  var dataUpdateTime: LocalDateTime? = null,
)