package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
@TableName("gxy_track")
data class TrackEntity(
    var id: Long? = null,

    var sourceId: String? =null,

    var albumId: Long? = null,

    var uid: Long? = null,

    var sessionCode: Long? = null,

    var phaseCode: Long? = null,

    var trackIndex: Int? = null,

    var parentTitle: String? = null,

    var trackName: String? = null,

    var trackContent: String? = null,

    var deletedAt: Int? = null,

    var createTime: LocalDateTime? = null,
    var updateTime: LocalDateTime? = null,

    var dataCreateTime: LocalDateTime? = null,
    var dataUpdateTime: LocalDateTime? = null,
)
