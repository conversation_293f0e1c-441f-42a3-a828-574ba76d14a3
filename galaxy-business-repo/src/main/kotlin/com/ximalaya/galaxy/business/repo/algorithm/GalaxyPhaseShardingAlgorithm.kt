package com.ximalaya.galaxy.business.repo.algorithm

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.support.GALAXY_PHASE_TABLE_NAME
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue

/**
 *<AUTHOR>
 *@create 2023-08-18 01:16
 */
class GalaxyPhaseShardingAlgorithm : PreciseShardingAlgorithm<Long> {

  override fun doSharding(availableTargetNames: Collection<String>?, shardingValue: PreciseShardingValue<Long>?): String {

    GalaxyAsserts.assertEquals(GALAXY_PHASE_TABLE_NAME, shardingValue?.logicTableName, ErrorCode.ASSERT_ERROR, "配置分表错误了哦~")

    val targetTableIndex = shardingValue!!.value % 20

    return availableTargetNames?.find { it.endsWith(targetTableIndex.toString()) } ?: throw GalaxyException(ErrorCode.SESSION_ERROR, "分表不正确")
  }

}