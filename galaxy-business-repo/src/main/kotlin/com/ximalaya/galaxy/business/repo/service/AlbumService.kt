package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.AlbumEntity

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
interface AlbumService : IService<AlbumEntity> {

    fun selectBySessionCode(sessionCode: Long): AlbumEntity?

    fun updateAlbum(id: Long, albumName: String?): Boolean

    fun deleteAlbum(id: Long): <PERSON><PERSON><PERSON>

}
