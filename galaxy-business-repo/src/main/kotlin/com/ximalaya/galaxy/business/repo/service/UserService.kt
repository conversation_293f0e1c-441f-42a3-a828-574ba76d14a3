package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.UserEntity

/**
 * 用户Service接口
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
interface UserService : IService<UserEntity> {
    
    /**
     * 根据用户名查找用户
     */
    fun findByUsername(username: String): UserEntity?
    
    /**
     * 根据ID查找用户
     */
    fun findById(id: Long): UserEntity?

    
    /**
     * 更新用户
     */
    fun update(user: UserEntity): Boolean
    
    /**
     * 更新最后登录信息
     */
    fun updateLastLoginInfo(id: Long, loginTime: java.time.LocalDateTime, loginIp: String?): Boolean
    
    /**
     * 检查用户名是否存在
     */
    fun existsByUsername(username: String): <PERSON><PERSON><PERSON>
    

} 