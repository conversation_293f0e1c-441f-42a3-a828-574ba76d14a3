package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import com.ximalaya.galaxy.business.repo.support.GALAXY_BLOCK_TABLE_NAME
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-14 14:17
 */
@TableName(GALAXY_BLOCK_TABLE_NAME)
data class GalaxyBlockEntity(
  var id: Long? = null,

  var sessionCode: Long? = null,

  /**
   * @see com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity.phaseCode
   */
  var phaseCode: Long? = null,

  /**
   * 雪花ID
   */
  var blockCode: Long? = null,

  /**
   * @see com.ximalaya.galaxy.business.repo.enums.BlockType
   */
  var blockType: Int? = null,

  /**
   * 响应内容 dify时使用
   */
  var response: String? = null,

  /**
   * 格式化后的内容
   */
  var content: String? = null,

  /**
   * 审核状态 0=未审核 1=送审中 2=审核通过 -1=审核不通过
   * @see com.ximalaya.galaxy.business.repo.enums.AuditState
   */
  var auditState: Int? = null,

  var auditRequest: String? = null,

  var auditResponse: String? = null,

  var deletedAt: Int? = null,

  var createTime: LocalDateTime? = null,
  var updateTime: LocalDateTime? = null,

  var dataCreateTime: LocalDateTime? = null,
  var dataUpdateTime: LocalDateTime? = null,
)