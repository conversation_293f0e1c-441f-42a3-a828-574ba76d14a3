package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.AlbumEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.AlbumMapper
import com.ximalaya.galaxy.business.repo.service.AlbumService
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 10:03
 */
@Repository
class AlbumServiceImpl : ServiceImpl<AlbumMapper, AlbumEntity>(), AlbumService {

    override fun selectBySessionCode(sessionCode: Long): AlbumEntity? {
        return ktQuery().eq(AlbumEntity::sessionCode, sessionCode)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
    }

    override fun updateAlbum(id: Long, albumName: String?): Boolean {
        val updateWrapper = ktUpdate().eq(AlbumEntity::id, id)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .set(AlbumEntity::albumName, albumName)
            .set(AlbumEntity::updateTime, LocalDateTime.now())

        return updateWrapper.update()
    }

    override fun deleteAlbum(id: Long): Boolean {
        return ktUpdate().eq(AlbumEntity::id, id)
            .set(AlbumEntity::deletedAt, LogicDeleted.DELETED.getCode())
            .set(AlbumEntity::updateTime, LocalDateTime.now())
            .update()
    }

}
