package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import com.ximalaya.galaxy.business.repo.support.GALAXY_PHASE_TABLE_NAME
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-14 15:17
 */
@TableName(GALAXY_PHASE_TABLE_NAME)
data class GalaxyPhaseEntity(
  var id: Long? = null,

  var sessionCode: Long? = null,

  /**
   * 雪花ID
   */
  var phaseCode: Long? = null,

  /**
   * 阶段名称
   */
  var phaseName: String? = null,

  /**
   * @see com.ximalaya.galaxy.business.repo.enums.PhaseState
   */
  var phaseState: Int? = null,

  var deletedAt: Int? = null,

  var createTime: LocalDateTime? = null,
  var updateTime: LocalDateTime? = null,

  var dataCreateTime: LocalDateTime? = null,
  var dataUpdateTime: LocalDateTime? = null,
)