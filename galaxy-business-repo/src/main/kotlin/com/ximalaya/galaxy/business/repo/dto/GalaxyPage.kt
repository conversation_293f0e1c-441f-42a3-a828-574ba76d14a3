package com.ximalaya.galaxy.business.repo.dto

import com.baomidou.mybatisplus.core.metadata.IPage
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 *<AUTHOR>
 *@create 2025-06-01 15:45
 */
@ApiModel(description = "分页结果")
class GalaxyPage<T> {

    @ApiModelProperty("记录列表")
    var records: List<T>? = emptyList()

    @ApiModelProperty("当前页")
    var current: Long = 1

    @ApiModelProperty("每页条数")
    var size: Long = 0

    @ApiModelProperty("总数量")
    var total: Long = 0

    @ApiModelProperty("总页数")
    var totalPages: Long = 0

    fun <U> map(mapper: (T) -> U): GalaxyPage<U> {
        return GalaxyPage<U>().apply {
            this.current = <EMAIL>
            this.size = <EMAIL>
            this.total = <EMAIL>
            this.totalPages = <EMAIL>
            this.records = <EMAIL>?.map {
                mapper.invoke(it)
            }?.toList()
        }
    }

    fun <U> mapList(mapper: (List<T>) -> List<U>): GalaxyPage<U> {
        return GalaxyPage<U>().apply {
            this.current = <EMAIL>
            this.size = <EMAIL>
            this.total = <EMAIL>
            this.totalPages = <EMAIL>
            this.records = <EMAIL>?.let {
                mapper.invoke(it)
            }
        }
    }

    companion object {

        fun <T> of(src: IPage<T>): GalaxyPage<T> {
            return GalaxyPage<T>().apply {
                this.current = src.current
                this.size = src.size
                this.total = src.total
                this.totalPages = src.pages
                this.records = src.records
            }
        }

    }
}