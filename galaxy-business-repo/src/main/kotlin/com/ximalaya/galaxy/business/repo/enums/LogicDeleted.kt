package com.ximalaya.galaxy.business.repo.enums

import com.ximalaya.galaxy.business.common.support.DateTimes
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2022-11-30 16:07
 */
enum class LogicDeleted {
  /**
   * 保存状态
   */
  SAVE {
    override fun getCode() = 0
  },

  /**
   * 删除状态 删除时间戳
   */
  DELETED {
    override fun getCode() = DateTimes.toBeijingUnixTimeSecond(LocalDateTime.now()).toInt()
  }
  ;

  abstract fun getCode(): Int
}