package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.GalaxyBlockMapper
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
@Repository
class GalaxyBlockServiceImpl : ServiceImpl<GalaxyBlockMapper, GalaxyBlockEntity>(), GalaxyBlockService {

    override fun createBlock(sessionCode: Long, phaseCode: Long, blockType: BlockType, content: String?): Long {
        val now = LocalDateTime.now()

        val blockEntity = GalaxyBlockEntity().apply {
            this.sessionCode = sessionCode
            this.phaseCode = phaseCode
            this.blockType = blockType.code
            this.content = content
            this.auditState = AuditState.INIT.code
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
        }

        GalaxyAsserts.assertTrue(save(blockEntity), ErrorCode.BLOCK_ERROR, "创建数据块失败")
        // blockCode
        return blockEntity.id!!
    }

    override fun createBlockAutoAuditSuccess(
        sessionCode: Long,
        phaseCode: Long,
        blockType: BlockType,
        content: String?
    ): Long {
        val now = LocalDateTime.now()

        val blockEntity = GalaxyBlockEntity().apply {
            this.sessionCode = sessionCode
            this.phaseCode = phaseCode
            this.blockType = blockType.code
            this.content = content
            this.auditState = AuditState.SUCCESS.code
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
        }

        GalaxyAsserts.assertTrue(save(blockEntity), ErrorCode.BLOCK_ERROR, "创建数据块失败")
        // blockCode
        return blockEntity.id!!
    }

    override fun updateBlock(sessionCode: Long, phaseCode: Long, blockCode: Long, content: String): Boolean {
        return ktUpdate().eq(GalaxyBlockEntity::sessionCode, sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, phaseCode)
            .eq(GalaxyBlockEntity::blockCode, blockCode)
            .set(GalaxyBlockEntity::content, content)
            .set(GalaxyBlockEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun updateAuditBlockResult(
        sessionCode: Long,
        phaseCode: Long,
        blockCode: Long,
        auditState: AuditState,
        auditRequest: String?,
        auditResponse: String?,
    ): Boolean {
        return ktUpdate().eq(GalaxyBlockEntity::sessionCode, sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, phaseCode)
            .eq(GalaxyBlockEntity::blockCode, blockCode)
            .set(GalaxyBlockEntity::auditState, auditState.code)
            .set(StringUtils.isNotBlank(auditRequest), GalaxyBlockEntity::auditRequest, auditRequest)
            .set(StringUtils.isNotBlank(auditResponse), GalaxyBlockEntity::auditResponse, auditResponse)
            .set(GalaxyBlockEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun deleteBlocks(sessionCode: Long, phaseCode: Long, blockCodes: Collection<Long>): Boolean {
        return ktUpdate().eq(GalaxyBlockEntity::sessionCode, sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, phaseCode)
            .`in`(GalaxyBlockEntity::blockCode, blockCodes)
            .set(GalaxyBlockEntity::deletedAt, LogicDeleted.DELETED.getCode())
            .set(GalaxyBlockEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun selectBySessionCodeAndPhaseCodeAndBlockCode(
        sessionCode: Long,
        phaseCode: Long,
        blockCode: Long
    ): GalaxyBlockEntity? {
        return ktQuery().eq(GalaxyBlockEntity::sessionCode, sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, phaseCode)
            .eq(GalaxyBlockEntity::blockCode, blockCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()
    }

}