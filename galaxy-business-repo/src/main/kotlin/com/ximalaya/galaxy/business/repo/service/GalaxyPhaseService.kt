package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
interface GalaxyPhaseService : IService<GalaxyPhaseEntity> {

  fun selectBySessionCode(sessionCode: Long): List<GalaxyPhaseEntity>

  fun selectByPhaseCodes(phaseCodes: Collection<Long>): List<GalaxyPhaseEntity>

  fun selectBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): GalaxyPhaseEntity?

  fun selectBySessionCodeAndPhaseName(sessionCode: Long, phaseName: String): GalaxyPhaseEntity?

  fun deleteBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): Boolean

  fun deleteNotOutlineBySessionCode(sessionCode: Long): Boolean

  fun deleteNotOutlineBySessionCodeAndPhaseName(sessionCode: Long, phaseName: String): Boolean

}