CREATE TABLE `gxy_invitation_code`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code`             varchar(30) NOT NULL COMMENT '邀请码',
    `code_state`       tinyint(1)  NOT NULL COMMENT '是否被删除',
    `uid`              varchar(30)          DEFAULT NULL COMMENT '注册uid',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    `permission_id`    int(11)  NOT NULL DEFAULT '0' COMMENT '权限ID：0-外部用户，1-内部用户，2-管理员等',
    `description`      varchar(255)         DEFAULT NULL COMMENT '邀请码描述',
    `expire_time`      datetime(3)          DEFAULT NULL COMMENT '过期时间',
    `creator_id`       bigint(20)           DEFAULT NULL COMMENT '创建者ID',
    `inviter_id`       bigint(20)           DEFAULT NULL COMMENT '邀请人ID',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uniq_code` (`code`),
    UNIQUE INDEX `uniq_uid` (`uid`)
) ENGINE = InnoDB COMMENT '邀请码';

CREATE TABLE `gxy_prompt`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`             varchar(50) NOT NULL COMMENT '名称',
    `content`          mediumtext  NOT NULL COMMENT '内容',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uniq_name` (`name`)
) ENGINE = InnoDB COMMENT 'prompt';

CREATE TABLE `gxy_tool`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tool_name`        varchar(50) NOT NULL COMMENT '名称',
    `tool_type`        tinyint(4)  NOT NULL COMMENT '工具类型 1=Dify',
    `metadata`         text                 DEFAULT NULL COMMENT '元数据',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uniq_tool_name` (`tool_name`)
) ENGINE = InnoDB COMMENT 'Galaxy工具表';

CREATE TABLE `gxy_album`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码',
    `album_name`       varchar(128)         DEFAULT NULL COMMENT '专辑名称',
    `deleted_at`       int(11)              DEFAULT 0 COMMENT '删除时间戳，0表示未删除',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uni_session_code` (`session_code`, `deleted_at`),
    INDEX `idx_uid` (`uid`)
) ENGINE = InnoDB COMMENT '专辑表';

CREATE TABLE `gxy_track`
(
    `id`               bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `source_id`        varchar(50)  NOT NULL COMMENT '源ID',
    `album_id`         bigint(20)   NOT NULL COMMENT '专辑ID',
    `uid`              bigint(20)   NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)   NOT NULL COMMENT '会话编码',
    `phase_code`       bigint(20)            DEFAULT NULL COMMENT '阶段编码',
    `track_index`      int(11)               DEFAULT 0 COMMENT '索引',
    `parent_title`     varchar(128)          DEFAULT NULL COMMENT '父标题',
    `track_name`       varchar(128) NOT NULL COMMENT '音轨名称',
    `track_content`    MEDIUMTEXT            DEFAULT NULL COMMENT '音轨内容',
    `deleted_at`       int(11)               DEFAULT 0 COMMENT '删除时间戳，0表示未删除',
    `create_time`      datetime(3)  NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3)  NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uniq_source_id` (`source_id`),
    INDEX `idx_session_code_phase_code` (`session_code`, `phase_code`, `deleted_at`),
    INDEX `idx_album_id` (`album_id`),
    INDEX `idx_uid` (`uid`)
) ENGINE = InnoDB COMMENT '声音表';

-- gxy_session 分表(0-9)
CREATE TABLE `gxy_session_0`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表0';

CREATE TABLE `gxy_session_1`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表1';

CREATE TABLE `gxy_session_2`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表2';

CREATE TABLE `gxy_session_3`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表3';

CREATE TABLE `gxy_session_4`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表4';

CREATE TABLE `gxy_session_5`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表5';

CREATE TABLE `gxy_session_6`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表6';

CREATE TABLE `gxy_session_7`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表7';

CREATE TABLE `gxy_session_8`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表8';

CREATE TABLE `gxy_session_9`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `business_data`    text                 DEFAULT NULL COMMENT '会话业务数据',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid_create_time` (`uid`, `create_time`),
    UNIQUE INDEX `uni_session_code` (`session_code`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表9';

-- gxy_phase 分表 (0-19)
CREATE TABLE `gxy_phase_0`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表0';

CREATE TABLE `gxy_phase_1`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表1';

CREATE TABLE `gxy_phase_2`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表2';

CREATE TABLE `gxy_phase_3`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表3';

CREATE TABLE `gxy_phase_4`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表4';

CREATE TABLE `gxy_phase_5`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表5';

CREATE TABLE `gxy_phase_6`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表6';

CREATE TABLE `gxy_phase_7`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表7';

CREATE TABLE `gxy_phase_8`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表8';

CREATE TABLE `gxy_phase_9`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表9';

CREATE TABLE `gxy_phase_10`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表10';

CREATE TABLE `gxy_phase_11`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表11';

CREATE TABLE `gxy_phase_12`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表12';

CREATE TABLE `gxy_phase_13`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表13';

CREATE TABLE `gxy_phase_14`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表14';

CREATE TABLE `gxy_phase_15`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表15';

CREATE TABLE `gxy_phase_16`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表16';

CREATE TABLE `gxy_phase_17`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表17';

CREATE TABLE `gxy_phase_18`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表18';

CREATE TABLE `gxy_phase_19`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表19';

-- gxy_block 分表 (0-19)
CREATE TABLE `gxy_block_0`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表0';

CREATE TABLE `gxy_block_1`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表1';

CREATE TABLE `gxy_block_2`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表2';

CREATE TABLE `gxy_block_3`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表3';

CREATE TABLE `gxy_block_4`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表4';

CREATE TABLE `gxy_block_5`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表5';

CREATE TABLE `gxy_block_6`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表6';

CREATE TABLE `gxy_block_7`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表7';

CREATE TABLE `gxy_block_8`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表8';

CREATE TABLE `gxy_block_9`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表9';

CREATE TABLE `gxy_block_10`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表10';

CREATE TABLE `gxy_block_11`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表11';

CREATE TABLE `gxy_block_12`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表12';

CREATE TABLE `gxy_block_13`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表13';

CREATE TABLE `gxy_block_14`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表14';

CREATE TABLE `gxy_block_15`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表15';

CREATE TABLE `gxy_block_16`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表16';

CREATE TABLE `gxy_block_17`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表17';

CREATE TABLE `gxy_block_18`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表18';

CREATE TABLE `gxy_block_19`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表19';

-- 用户表
-- galaxy_business.gxy_user definition

CREATE TABLE `gxy_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（加密）',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `permission_id` int(11) NOT NULL DEFAULT '0' COMMENT '权限ID：0-外部用户，1-内部用户，2-管理员等',
  `invitation_code` varchar(50) DEFAULT NULL COMMENT '注册时使用的邀请码',
  `inviter_id` bigint(20) DEFAULT NULL COMMENT '邀请人ID',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `data_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `data_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_status` (`status`),
  KEY `idx_inviter_id` (`inviter_id`)
) ENGINE=InnoDB AUTO_INCREMENT=56000 DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户Token表
CREATE TABLE IF NOT EXISTS `gxy_user_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `token` varchar(255) NOT NULL COMMENT 'Token',
  `refresh_token` varchar(255) DEFAULT NULL COMMENT '刷新Token',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `refresh_expire_time` datetime DEFAULT NULL COMMENT '刷新Token过期时间',
  `device_info` varchar(255) DEFAULT NULL COMMENT '设备信息',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `data_create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `data_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_token` (`token`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户Token表';

-- 权限表
CREATE TABLE IF NOT EXISTS `gxy_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `description` varchar(255) DEFAULT NULL COMMENT '权限描述',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 插入默认权限数据
INSERT INTO `gxy_permission` (`id`, `name`, `description`) VALUES 
(0, 'EXTERNAL_USER', '外部用户'),
(1, 'INTERNAL_USER', '内部用户'),
(2, 'ADMIN', '管理员')
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `description` = VALUES(`description`);

-- 用户登录日志表
CREATE TABLE IF NOT EXISTS `gxy_user_login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `login_time` datetime NOT NULL COMMENT '登录时间',
  `login_ip` varchar(50) DEFAULT NULL COMMENT '登录IP',
  `device_info` varchar(255) DEFAULT NULL COMMENT '设备信息',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `status` tinyint(4) NOT NULL COMMENT '状态：1-成功，0-失败',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';