namespace java com.ximalaya.galaxy.business.worker.api.thrift.entity

struct StartSessionJobRequest {

  1: i64 uid;

  2: i64 sessionCode;

  3: i64 phaseCode;

  4: string systemPrompt;

  5: string prompt;

}

struct StartToolJobRequest {

  1: i64 uid;

  2: i64 sessionCode;

  3: i64 phaseCode;

  4: i64 toolId;

  5: string args;

  6: list<i64> needRemoveBlockCodes;

}

struct CreateSessionJobRequest {

  1: i64 uid;

  2: string phaseName;

  3: string systemPrompt;

  4: string prompt;

  5: string businessData;

}

struct CreateAndStartPhaseJobRequest {

  1: i64 uid;

  2: i64 sessionCode;

  3: string phaseName;

  4: string systemPrompt;

  5: string prompt;

}

struct CreateAndStartToolJobRequest {

  1: i64 uid;

  2: i64 sessionCode;

  3: string phaseName;

  4: i64 toolId;

  5: string args;

  6: list<i64> needRemoveBlockCodes;

}

struct CommonJobResponse {

  /** 结果状态 200-成功 other-失败*/
  1: i32 code;

  /** 结果消息 */
  2: string message;

  3: string data;

}
