/**
 * Generated by <PERSON><PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.converter;

import java.nio.ByteBuffer;
import java.util.*;


public class CreateAndStartPhaseJobRequestConverter {

  public static com.ximalaya.galaxy.business.worker.api.model.CreateAndStartPhaseJobRequest transform(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.model.CreateAndStartPhaseJobRequest target = new com.ximalaya.galaxy.business.worker.api.model.CreateAndStartPhaseJobRequest();
    if (null != source) {
      if (source.isSetUid()) {
        Long _uid_item = source.getUid();
        target.setUid(_uid_item);
      }
      if (source.isSetSessionCode()) {
        Long _sessionCode_item = source.getSessionCode();
        target.setSessionCode(_sessionCode_item);
      }
      if (source.isSetPhaseName()) {
        String _phaseName_item = source.getPhaseName();
        target.setPhaseName(_phaseName_item);
      }
      if (source.isSetSystemPrompt()) {
        String _systemPrompt_item = source.getSystemPrompt();
        target.setSystemPrompt(_systemPrompt_item);
      }
      if (source.isSetPrompt()) {
        String _prompt_item = source.getPrompt();
        target.setPrompt(_prompt_item);
      }
    }
    return target;
  }

  public static com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest transform(com.ximalaya.galaxy.business.worker.api.model.CreateAndStartPhaseJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest.Builder target = new com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest.Builder();
    if (null != source) {
      if (source.getUid() != null) {
        Long _uid_item = source.getUid();
        target.setUid(_uid_item);
      }
      if (source.getSessionCode() != null) {
        Long _sessionCode_item = source.getSessionCode();
        target.setSessionCode(_sessionCode_item);
      }
      if (source.getPhaseName() != null) {
        String _phaseName_item = source.getPhaseName();
        target.setPhaseName(_phaseName_item);
      }
      if (source.getSystemPrompt() != null) {
        String _systemPrompt_item = source.getSystemPrompt();
        target.setSystemPrompt(_systemPrompt_item);
      }
      if (source.getPrompt() != null) {
        String _prompt_item = source.getPrompt();
        target.setPrompt(_prompt_item);
      }
    }
    return target.build();
  }
}