/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.service;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.ximalaya.mainstay.common.RouteArg;


public interface IRemoteSyncGalaxyBusinessWorkerJobService {
    
    public com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse startSessionJob(com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest startRequest);
    
    public com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse startToolJob(com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest startRequest);
    
    public com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse createSessionJob(com.ximalaya.galaxy.business.worker.api.model.CreateSessionJobRequest createRequest);
    
    public com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse createAndStartSessionJob(com.ximalaya.galaxy.business.worker.api.model.CreateAndStartPhaseJobRequest startRequest);
    
    public com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse createAndStartToolJob(com.ximalaya.galaxy.business.worker.api.model.CreateAndStartToolJobRequest startRequest);
  
}