/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.converter;

import java.nio.ByteBuffer;
import java.util.*;


public class CommonJobResponseConverter {

  public static com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse transform(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse source) {
    com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse target = new com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse();
    if (null != source) {
      if (source.isSetCode()) {
        Integer _code_item = source.getCode();
        target.setCode(_code_item);
      }
      if (source.isSetMessage()) {
        String _message_item = source.getMessage();
        target.setMessage(_message_item);
      }
      if (source.isSetData()) {
        String _data_item = source.getData();
        target.setData(_data_item);
      }
    }
    return target;
  }

  public static com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse transform(com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse source) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse.Builder target = new com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse.Builder();
    if (null != source) {
      if (source.getCode() != null) {
        Integer _code_item = source.getCode();
        target.setCode(_code_item);
      }
      if (source.getMessage() != null) {
        String _message_item = source.getMessage();
        target.setMessage(_message_item);
      }
      if (source.getData() != null) {
        String _data_item = source.getData();
        target.setData(_data_item);
      }
    }
    return target.build();
  }
}