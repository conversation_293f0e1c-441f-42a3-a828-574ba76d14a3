/**
 * Generated by <PERSON><PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.thrift.entity;

import java.nio.ByteBuffer;
import java.util.*;

import com.ximalaya.mainstay.common.MainstayCodecException;
import com.ximalaya.mainstay.common.Option;
import com.ximalaya.mainstay.common.RouteArg;
import com.ximalaya.mainstay.common.util.Utilities;
import com.ximalaya.mainstay.rpc.thrift.ThriftStruct;
import com.ximalaya.mainstay.rpc.thrift.ThriftStructCodec;
import com.ximalaya.mainstay.rpc.thrift.TException;
import com.ximalaya.mainstay.rpc.thrift.protocol.*;


public class PingRequest implements ThriftStruct {
  private static final TStruct STRUCT = new TStruct("PingRequest");
  private static final TField MessageField = new TField("message", TType.STRING, (short) 1);
  final String message;
  boolean isSetMessage;

  public static class Builder {
    private String _message = null;
    private boolean isSetMessage = false;
    public Builder setMessage(String value) {
      this._message = value;
      if (value != null) {
        this.isSetMessage = true;
      }
      return this;
    }


    public boolean isSetMessage() {
      return isSetMessage;
    }

    public String getMessage() {
      return this._message;
    }

    public Builder unsetMessage() {
      this._message = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyMessage() {
      this._message = null;
      this.isSetMessage = true;
      return this;
    }


    public PingRequest build() {
      PingRequest model = new PingRequest(this._message);
      model.isSetMessage = isSetMessage;
      return model;
    }
  }

  public Builder copy() {
    Builder builder = new Builder();
    if (isSetMessage) builder.setMessage(this.message);
    return builder;
  }

  public static final ThriftStructCodec<PingRequest> CODEC = new ThriftStructCodec<PingRequest>() {
    @Override
    public PingRequest decode(TProtocol _iprot) throws TException {
      Builder builder = new Builder();
      String message = null;
      Boolean _done = false;
      _iprot.readStructBegin();
      while (!_done) {
        TField _field = _iprot.readFieldBegin();
        if (_field.type == TType.STOP) {
          _done = true;
        } else {
          switch (_field.id) {
            case 1: /* message */
              switch (_field.type) {
                case TType.STRING:
                  String message_item;
                  message_item = _iprot.readString();
                  message = message_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setMessage(message);
              break;
            default:
              TProtocolUtil.skip(_iprot, _field.type);
          }
          _iprot.readFieldEnd();
        }
      }
      _iprot.readStructEnd();
      try {
        return builder.build();
      } catch (IllegalStateException stateEx) {
        throw new TProtocolException(stateEx.getMessage());
      }
    }

    @Override
    public void encode(PingRequest struct, TProtocol oprot) throws TException {
      struct.write(oprot);
    }
  };

  public static PingRequest decode(TProtocol _iprot) throws TException {
    return CODEC.decode(_iprot);
  }

  public static void encode(PingRequest struct, TProtocol oprot) throws TException {
    CODEC.encode(struct, oprot);
  }

  PingRequest(String message) {
    this.message = message;
  }


  public String getMessage() {
    return this.message;
  }
  
  public boolean isSetMessage() {
    return this.isSetMessage;
  }

  @Override
  public void write(TProtocol _oprot) throws TException {
    validate();
    _oprot.writeStructBegin(STRUCT);
    if (this.isSetMessage) {
      if (message != null) {
        _oprot.writeFieldBegin(MessageField);
        String message_item = message;
        _oprot.writeString(message_item);
        _oprot.writeFieldEnd();
      }
    }
    _oprot.writeFieldStop();
    _oprot.writeStructEnd();
  }

  private void validate() throws TProtocolException {
  }


  @Override
  public boolean equals(Object other) {
    if (!(other instanceof PingRequest)) {
      return false;
    }
    PingRequest that = (PingRequest) other;
    if (this.message == null) {
      if (that.message != null)
        return false;
    } else if (!this.message.equals(that.message)) {
      return false;
    }
    return true;
  }

  private int hash(Object... values) {
    return java.util.Arrays.hashCode(values);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("PingRequest(");
    if (this.isSetMessage) {
      sb.append("message=");
      sb.append(this.message);
      sb.append(", ");
    }
    sb.append(")");
    return sb.toString();
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + hash * (this.message == null ? 0 : this.message.hashCode());
    return hash;
  }
}