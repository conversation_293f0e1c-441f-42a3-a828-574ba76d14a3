/**
 * Generated by <PERSON><PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.thrift.entity;

import java.nio.ByteBuffer;
import java.util.*;

import com.ximalaya.mainstay.common.MainstayCodecException;
import com.ximalaya.mainstay.common.Option;
import com.ximalaya.mainstay.common.RouteArg;
import com.ximalaya.mainstay.common.util.Utilities;
import com.ximalaya.mainstay.rpc.thrift.ThriftStruct;
import com.ximalaya.mainstay.rpc.thrift.ThriftStructCodec;
import com.ximalaya.mainstay.rpc.thrift.TException;
import com.ximalaya.mainstay.rpc.thrift.protocol.*;


public class CreateSessionJobRequest implements ThriftStruct {
  private static final TStruct STRUCT = new TStruct("CreateSessionJobRequest");
  private static final TField UidField = new TField("uid", TType.I64, (short) 1);
  final long uid;
  boolean isSetUid;
  private static final TField PhaseNameField = new TField("phaseName", TType.STRING, (short) 2);
  final String phaseName;
  boolean isSetPhaseName;
  private static final TField SystemPromptField = new TField("systemPrompt", TType.STRING, (short) 3);
  final String systemPrompt;
  boolean isSetSystemPrompt;
  private static final TField PromptField = new TField("prompt", TType.STRING, (short) 4);
  final String prompt;
  boolean isSetPrompt;
  private static final TField BusinessDataField = new TField("businessData", TType.STRING, (short) 5);
  final String businessData;
  boolean isSetBusinessData;

  public static class Builder {
    private long _uid = 0L;
    private boolean isSetUid = false;
    public Builder setUid(long value) {
      this._uid = value;
        this.isSetUid = true;
      return this;
    }


    public boolean isSetUid() {
      return isSetUid;
    }

    public long getUid() {
      return this._uid;
    }

    public Builder unsetUid() {
      this._uid = 0L;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyUid() {
      this._uid = 0L;
      this.isSetUid = true;
      return this;
    }

    private String _phaseName = null;
    private boolean isSetPhaseName = false;
    public Builder setPhaseName(String value) {
      this._phaseName = value;
      if (value != null) {
        this.isSetPhaseName = true;
      }
      return this;
    }


    public boolean isSetPhaseName() {
      return isSetPhaseName;
    }

    public String getPhaseName() {
      return this._phaseName;
    }

    public Builder unsetPhaseName() {
      this._phaseName = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyPhaseName() {
      this._phaseName = null;
      this.isSetPhaseName = true;
      return this;
    }

    private String _systemPrompt = null;
    private boolean isSetSystemPrompt = false;
    public Builder setSystemPrompt(String value) {
      this._systemPrompt = value;
      if (value != null) {
        this.isSetSystemPrompt = true;
      }
      return this;
    }


    public boolean isSetSystemPrompt() {
      return isSetSystemPrompt;
    }

    public String getSystemPrompt() {
      return this._systemPrompt;
    }

    public Builder unsetSystemPrompt() {
      this._systemPrompt = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptySystemPrompt() {
      this._systemPrompt = null;
      this.isSetSystemPrompt = true;
      return this;
    }

    private String _prompt = null;
    private boolean isSetPrompt = false;
    public Builder setPrompt(String value) {
      this._prompt = value;
      if (value != null) {
        this.isSetPrompt = true;
      }
      return this;
    }


    public boolean isSetPrompt() {
      return isSetPrompt;
    }

    public String getPrompt() {
      return this._prompt;
    }

    public Builder unsetPrompt() {
      this._prompt = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyPrompt() {
      this._prompt = null;
      this.isSetPrompt = true;
      return this;
    }

    private String _businessData = null;
    private boolean isSetBusinessData = false;
    public Builder setBusinessData(String value) {
      this._businessData = value;
      if (value != null) {
        this.isSetBusinessData = true;
      }
      return this;
    }


    public boolean isSetBusinessData() {
      return isSetBusinessData;
    }

    public String getBusinessData() {
      return this._businessData;
    }

    public Builder unsetBusinessData() {
      this._businessData = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyBusinessData() {
      this._businessData = null;
      this.isSetBusinessData = true;
      return this;
    }


    public CreateSessionJobRequest build() {
      CreateSessionJobRequest model = new CreateSessionJobRequest(this._uid, this._phaseName, this._systemPrompt, this._prompt, this._businessData);
      model.isSetUid = isSetUid;
      model.isSetPhaseName = isSetPhaseName;
      model.isSetSystemPrompt = isSetSystemPrompt;
      model.isSetPrompt = isSetPrompt;
      model.isSetBusinessData = isSetBusinessData;
      return model;
    }
  }

  public Builder copy() {
    Builder builder = new Builder();
    if (isSetUid) builder.setUid(this.uid);
    if (isSetPhaseName) builder.setPhaseName(this.phaseName);
    if (isSetSystemPrompt) builder.setSystemPrompt(this.systemPrompt);
    if (isSetPrompt) builder.setPrompt(this.prompt);
    if (isSetBusinessData) builder.setBusinessData(this.businessData);
    return builder;
  }

  public static final ThriftStructCodec<CreateSessionJobRequest> CODEC = new ThriftStructCodec<CreateSessionJobRequest>() {
    @Override
    public CreateSessionJobRequest decode(TProtocol _iprot) throws TException {
      Builder builder = new Builder();
      long uid = 0L;
      String phaseName = null;
      String systemPrompt = null;
      String prompt = null;
      String businessData = null;
      Boolean _done = false;
      _iprot.readStructBegin();
      while (!_done) {
        TField _field = _iprot.readFieldBegin();
        if (_field.type == TType.STOP) {
          _done = true;
        } else {
          switch (_field.id) {
            case 1: /* uid */
              switch (_field.type) {
                case TType.I64:
                  Long uid_item;
                  uid_item = _iprot.readI64();
                  uid = uid_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setUid(uid);
              break;
            case 2: /* phaseName */
              switch (_field.type) {
                case TType.STRING:
                  String phaseName_item;
                  phaseName_item = _iprot.readString();
                  phaseName = phaseName_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setPhaseName(phaseName);
              break;
            case 3: /* systemPrompt */
              switch (_field.type) {
                case TType.STRING:
                  String systemPrompt_item;
                  systemPrompt_item = _iprot.readString();
                  systemPrompt = systemPrompt_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setSystemPrompt(systemPrompt);
              break;
            case 4: /* prompt */
              switch (_field.type) {
                case TType.STRING:
                  String prompt_item;
                  prompt_item = _iprot.readString();
                  prompt = prompt_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setPrompt(prompt);
              break;
            case 5: /* businessData */
              switch (_field.type) {
                case TType.STRING:
                  String businessData_item;
                  businessData_item = _iprot.readString();
                  businessData = businessData_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setBusinessData(businessData);
              break;
            default:
              TProtocolUtil.skip(_iprot, _field.type);
          }
          _iprot.readFieldEnd();
        }
      }
      _iprot.readStructEnd();
      try {
        return builder.build();
      } catch (IllegalStateException stateEx) {
        throw new TProtocolException(stateEx.getMessage());
      }
    }

    @Override
    public void encode(CreateSessionJobRequest struct, TProtocol oprot) throws TException {
      struct.write(oprot);
    }
  };

  public static CreateSessionJobRequest decode(TProtocol _iprot) throws TException {
    return CODEC.decode(_iprot);
  }

  public static void encode(CreateSessionJobRequest struct, TProtocol oprot) throws TException {
    CODEC.encode(struct, oprot);
  }

  CreateSessionJobRequest(long uid, String phaseName, String systemPrompt, String prompt, String businessData) {
    this.uid = uid;
    this.phaseName = phaseName;
    this.systemPrompt = systemPrompt;
    this.prompt = prompt;
    this.businessData = businessData;
  }


  public long getUid() {
    return this.uid;
  }
  
  public boolean isSetUid() {
    return this.isSetUid;
  }
  public String getPhaseName() {
    return this.phaseName;
  }
  
  public boolean isSetPhaseName() {
    return this.isSetPhaseName;
  }
  public String getSystemPrompt() {
    return this.systemPrompt;
  }
  
  public boolean isSetSystemPrompt() {
    return this.isSetSystemPrompt;
  }
  public String getPrompt() {
    return this.prompt;
  }
  
  public boolean isSetPrompt() {
    return this.isSetPrompt;
  }
  public String getBusinessData() {
    return this.businessData;
  }
  
  public boolean isSetBusinessData() {
    return this.isSetBusinessData;
  }

  @Override
  public void write(TProtocol _oprot) throws TException {
    validate();
    _oprot.writeStructBegin(STRUCT);
    if (this.isSetUid) {
        _oprot.writeFieldBegin(UidField);
        Long uid_item = uid;
        _oprot.writeI64(uid_item);
        _oprot.writeFieldEnd();
    }
    if (this.isSetPhaseName) {
      if (phaseName != null) {
        _oprot.writeFieldBegin(PhaseNameField);
        String phaseName_item = phaseName;
        _oprot.writeString(phaseName_item);
        _oprot.writeFieldEnd();
      }
    }
    if (this.isSetSystemPrompt) {
      if (systemPrompt != null) {
        _oprot.writeFieldBegin(SystemPromptField);
        String systemPrompt_item = systemPrompt;
        _oprot.writeString(systemPrompt_item);
        _oprot.writeFieldEnd();
      }
    }
    if (this.isSetPrompt) {
      if (prompt != null) {
        _oprot.writeFieldBegin(PromptField);
        String prompt_item = prompt;
        _oprot.writeString(prompt_item);
        _oprot.writeFieldEnd();
      }
    }
    if (this.isSetBusinessData) {
      if (businessData != null) {
        _oprot.writeFieldBegin(BusinessDataField);
        String businessData_item = businessData;
        _oprot.writeString(businessData_item);
        _oprot.writeFieldEnd();
      }
    }
    _oprot.writeFieldStop();
    _oprot.writeStructEnd();
  }

  private void validate() throws TProtocolException {
  }


  @Override
  public boolean equals(Object other) {
    if (!(other instanceof CreateSessionJobRequest)) {
      return false;
    }
    CreateSessionJobRequest that = (CreateSessionJobRequest) other;
    if (this.uid != that.uid) {
      return false;
    }
    if (this.phaseName == null) {
      if (that.phaseName != null)
        return false;
    } else if (!this.phaseName.equals(that.phaseName)) {
      return false;
    }
    if (this.systemPrompt == null) {
      if (that.systemPrompt != null)
        return false;
    } else if (!this.systemPrompt.equals(that.systemPrompt)) {
      return false;
    }
    if (this.prompt == null) {
      if (that.prompt != null)
        return false;
    } else if (!this.prompt.equals(that.prompt)) {
      return false;
    }
    if (this.businessData == null) {
      if (that.businessData != null)
        return false;
    } else if (!this.businessData.equals(that.businessData)) {
      return false;
    }
    return true;
  }

  private int hash(Object... values) {
    return java.util.Arrays.hashCode(values);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CreateSessionJobRequest(");
    if (this.isSetUid) {
      sb.append("uid=");
      sb.append(this.uid);
      sb.append(", ");
    }
    if (this.isSetPhaseName) {
      sb.append("phaseName=");
      sb.append(this.phaseName);
      sb.append(", ");
    }
    if (this.isSetSystemPrompt) {
      sb.append("systemPrompt=");
      sb.append(this.systemPrompt);
      sb.append(", ");
    }
    if (this.isSetPrompt) {
      sb.append("prompt=");
      sb.append(this.prompt);
      sb.append(", ");
    }
    if (this.isSetBusinessData) {
      sb.append("businessData=");
      sb.append(this.businessData);
      sb.append(", ");
    }
    sb.append(")");
    return sb.toString();
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + hash * hash(this.uid);
    hash = 31 * hash + hash * (this.phaseName == null ? 0 : this.phaseName.hashCode());
    hash = 31 * hash + hash * (this.systemPrompt == null ? 0 : this.systemPrompt.hashCode());
    hash = 31 * hash + hash * (this.prompt == null ? 0 : this.prompt.hashCode());
    hash = 31 * hash + hash * (this.businessData == null ? 0 : this.businessData.hashCode());
    return hash;
  }
}