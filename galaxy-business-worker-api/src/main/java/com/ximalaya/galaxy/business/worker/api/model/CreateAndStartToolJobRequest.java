/**
 * Generated by Dal<PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.model;

import java.nio.ByteBuffer;
import java.util.*;


public class CreateAndStartToolJobRequest {

  private Long uid;
  private Long sessionCode;
  private String phaseName;
  private Long toolId;
  private String args;
  private List<Long> needRemoveBlockCodes;

  public CreateAndStartToolJobRequest() {
  }

  public CreateAndStartToolJobRequest(Long uid, Long sessionCode, String phaseName, Long toolId, String args, List<Long> needRemoveBlockCodes) {
    this.uid = uid;
    this.sessionCode = sessionCode;
    this.phaseName = phaseName;
    this.toolId = toolId;
    this.args = args;
    this.needRemoveBlockCodes = needRemoveBlockCodes;
  }

  public void setUid(Long uid) {
    this.uid = uid;
  }

  public Long getUid() {
    return this.uid;
  }

  public void setSessionCode(Long sessionCode) {
    this.sessionCode = sessionCode;
  }

  public Long getSessionCode() {
    return this.sessionCode;
  }

  public void setPhaseName(String phaseName) {
    this.phaseName = phaseName;
  }

  public String getPhaseName() {
    return this.phaseName;
  }

  public void setToolId(Long toolId) {
    this.toolId = toolId;
  }

  public Long getToolId() {
    return this.toolId;
  }

  public void setArgs(String args) {
    this.args = args;
  }

  public String getArgs() {
    return this.args;
  }

  public void setNeedRemoveBlockCodes(List<Long> needRemoveBlockCodes) {
    this.needRemoveBlockCodes = needRemoveBlockCodes;
  }

  public List<Long> getNeedRemoveBlockCodes() {
    return this.needRemoveBlockCodes;
  }

  @Override
  public String toString() {
    return "CreateAndStartToolJobRequest(" +"uid=" + this.uid + ", " + "sessionCode=" + this.sessionCode + ", " + "phaseName=" + this.phaseName + ", " + "toolId=" + this.toolId + ", " + "args=" + this.args + ", " + "needRemoveBlockCodes=" + this.needRemoveBlockCodes + ")";
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + (this.uid == null ? 0 : this.uid.hashCode());
    hash = 31 * hash + (this.sessionCode == null ? 0 : this.sessionCode.hashCode());
    hash = 31 * hash + (this.phaseName == null ? 0 : this.phaseName.hashCode());
    hash = 31 * hash + (this.toolId == null ? 0 : this.toolId.hashCode());
    hash = 31 * hash + (this.args == null ? 0 : this.args.hashCode());
    hash = 31 * hash + (this.needRemoveBlockCodes == null ? 0 : this.needRemoveBlockCodes.hashCode());
    return hash;
  }

  @Override
  public boolean equals(Object other) {
    if (!(other instanceof CreateAndStartToolJobRequest)) {
      return false;
    }
    CreateAndStartToolJobRequest that = (CreateAndStartToolJobRequest) other;
    if (this.uid == null) {
      if (that.uid != null)
        return false;
    } else if (!this.uid.equals(that.uid)) {
      return false;
    }
    if (this.sessionCode == null) {
      if (that.sessionCode != null)
        return false;
    } else if (!this.sessionCode.equals(that.sessionCode)) {
      return false;
    }
    if (this.phaseName == null) {
      if (that.phaseName != null)
        return false;
    } else if (!this.phaseName.equals(that.phaseName)) {
      return false;
    }
    if (this.toolId == null) {
      if (that.toolId != null)
        return false;
    } else if (!this.toolId.equals(that.toolId)) {
      return false;
    }
    if (this.args == null) {
      if (that.args != null)
        return false;
    } else if (!this.args.equals(that.args)) {
      return false;
    }
    if (this.needRemoveBlockCodes == null) {
      if (that.needRemoveBlockCodes != null)
        return false;
    } else if (!this.needRemoveBlockCodes.equals(that.needRemoveBlockCodes)) {
      return false;
    }
    return true;
  }
}