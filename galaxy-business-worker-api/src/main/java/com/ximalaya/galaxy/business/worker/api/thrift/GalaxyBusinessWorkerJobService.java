/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.thrift;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ximalaya.mainstay.common.MainstayCodecException;
import com.ximalaya.mainstay.common.Option;
import com.ximalaya.mainstay.common.RouteArg;
import com.ximalaya.mainstay.common.concurrent.FutureCallback;
import com.ximalaya.mainstay.common.concurrent.Futures;
import com.ximalaya.mainstay.common.concurrent.Future;
import com.ximalaya.mainstay.common.concurrent.SettableFuture;
import com.ximalaya.mainstay.common.util.Utilities;
import com.ximalaya.mainstay.rpc.thrift.processor.TBaseProcessor;
import com.ximalaya.mainstay.rpc.thrift.processor.TProcessFunction;
import com.ximalaya.mainstay.rpc.thrift.processor.TProcessor;
import com.ximalaya.mainstay.rpc.thrift.TCodec;
import com.ximalaya.mainstay.rpc.thrift.TFutureClient;
import com.ximalaya.mainstay.rpc.thrift.ThriftMessage;
import com.ximalaya.mainstay.rpc.thrift.ThriftStruct;
import com.ximalaya.mainstay.rpc.thrift.ThriftStructCodec;
import com.ximalaya.mainstay.rpc.thrift.TApplicationException;
import com.ximalaya.mainstay.rpc.thrift.TException;
import com.ximalaya.mainstay.rpc.thrift.protocol.TField;
import com.ximalaya.mainstay.rpc.thrift.protocol.TList;
import com.ximalaya.mainstay.rpc.thrift.protocol.TMap;
import com.ximalaya.mainstay.rpc.thrift.protocol.TSet;
import com.ximalaya.mainstay.rpc.thrift.protocol.TProtocol;
import com.ximalaya.mainstay.rpc.thrift.protocol.TProtocolException;
import com.ximalaya.mainstay.rpc.thrift.protocol.TProtocolFactory;
import com.ximalaya.mainstay.rpc.thrift.protocol.TProtocolUtil;
import com.ximalaya.mainstay.rpc.thrift.protocol.TStruct;
import com.ximalaya.mainstay.rpc.thrift.protocol.TType;
import com.ximalaya.mainstay.transport.Client;
import com.ximalaya.mainstay.transport.Message;


public class GalaxyBusinessWorkerJobService {
  public static interface Iface {
    
    public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse startSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest startRequest) throws TException;
    
    public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse startToolJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest startRequest) throws TException;
    
    public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse createSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest createRequest) throws TException;
    
    public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse createAndStartSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest startRequest) throws TException;
    
    public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse createAndStartToolJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest startRequest) throws TException;
  }

  public static interface FutureIface {
    
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> startSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest startRequest);
    
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> startToolJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest startRequest);
    
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> createSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest createRequest);
    
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> createAndStartSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest startRequest);
    
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> createAndStartToolJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest startRequest);
  }
  
  public static class StartSessionJob {
  public static class Args implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("startSessionJob_args");
      private static final TField StartRequestField = new TField("startRequest", TType.STRUCT, (short) 1);
      final com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest startRequest;
      boolean isSetStartRequest;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest _startRequest = null;
        private boolean isSetStartRequest = false;
        public Builder setStartRequest(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest value) {
          this._startRequest = value;
          if (value != null) {
            this.isSetStartRequest = true;
          }
          return this;
        }
    
    
        public boolean isSetStartRequest() {
          return isSetStartRequest;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest getStartRequest() {
          return this._startRequest;
        }
    
        public Builder unsetStartRequest() {
          this._startRequest = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptyStartRequest() {
          this._startRequest = null;
          this.isSetStartRequest = true;
          return this;
        }
    
    
        public Args build() {
          Args model = new Args(this._startRequest);
          model.isSetStartRequest = isSetStartRequest;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (isSetStartRequest) builder.setStartRequest(this.startRequest);
        return builder;
      }
    
      public static final ThriftStructCodec<Args> CODEC = new ThriftStructCodec<Args>() {
        @Override
        public Args decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest startRequest = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 1: /* startRequest */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest startRequest_item;
                      startRequest_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest.decode(_iprot);
                      startRequest = startRequest_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setStartRequest(startRequest);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Args struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Args decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Args struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Args(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest startRequest) {
        this.startRequest = startRequest;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest getStartRequest() {
        return this.startRequest;
      }
  
      public boolean isSetStartRequest() {
        return this.isSetStartRequest;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetStartRequest) {
          if (startRequest != null) {
            _oprot.writeFieldBegin(StartRequestField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest startRequest_item = startRequest;
            startRequest_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Args)) {
          return false;
        }
        Args that = (Args) other;
        if (this.startRequest == null) {
          if (that.startRequest != null)
            return false;
        } else if (!this.startRequest.equals(that.startRequest)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Args(");
        if (this.isSetStartRequest) {
          sb.append("startRequest=");
          sb.append(this.startRequest);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.startRequest == null ? 0 : this.startRequest.hashCode());
        return hash;
      }
    }
  public static class Result implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("startSessionJob_result");
      private static final TField SuccessField = new TField("success", TType.STRUCT, (short) 0);
      final Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success;
      boolean isSetSuccess;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse _success = null;
        private boolean isSetSuccess = false;
        public Builder setSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse value) {
          this._success = value;
          if (value != null) {
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public Builder setSuccessOpt(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> value) {
          if (value.isDefined()) {
            this._success = value.get();
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public boolean isSetSuccess() {
          return isSetSuccess;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
          return this._success;
        }
    
        public Builder unsetSuccess() {
          this._success = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptySuccess() {
          this._success = null;
          this.isSetSuccess = true;
          return this;
        }
    
    
        public Result build() {
          Result model = new Result(Option.fromNullable(this._success) );
          model.isSetSuccess = isSetSuccess;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (this.success.isDefined() && isSetSuccess) builder.setSuccess(this.success.get());
        return builder;
      }
    
      public static final ThriftStructCodec<Result> CODEC = new ThriftStructCodec<Result>() {
        @Override
        public Result decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 0: /* success */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item;
                      success_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse.decode(_iprot);
                      success = success_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setSuccess(success);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Result struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Result decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Result struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Result(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success) {
        this.success = success;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
        return this.success.get();
      }
        public Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> getSuccessOpt() {
        return this.success;
      }
  
      public boolean isSetSuccess() {
        return this.isSetSuccess;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetSuccess) {
          if (success.isDefined()) {
            _oprot.writeFieldBegin(SuccessField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item = success.get();
            success_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Result)) {
          return false;
        }
        Result that = (Result) other;
        if (this.success == null) {
          if (that.success != null)
            return false;
        } else if (!this.success.equals(that.success)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Result(");
        if (this.isSetSuccess) {
          sb.append("success=");
          sb.append(this.success);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.success.isDefined() ? this.success.get().hashCode() : 0);
        return hash;
      }
    }
  }
  public static class StartToolJob {
  public static class Args implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("startToolJob_args");
      private static final TField StartRequestField = new TField("startRequest", TType.STRUCT, (short) 1);
      final com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest startRequest;
      boolean isSetStartRequest;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest _startRequest = null;
        private boolean isSetStartRequest = false;
        public Builder setStartRequest(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest value) {
          this._startRequest = value;
          if (value != null) {
            this.isSetStartRequest = true;
          }
          return this;
        }
    
    
        public boolean isSetStartRequest() {
          return isSetStartRequest;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest getStartRequest() {
          return this._startRequest;
        }
    
        public Builder unsetStartRequest() {
          this._startRequest = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptyStartRequest() {
          this._startRequest = null;
          this.isSetStartRequest = true;
          return this;
        }
    
    
        public Args build() {
          Args model = new Args(this._startRequest);
          model.isSetStartRequest = isSetStartRequest;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (isSetStartRequest) builder.setStartRequest(this.startRequest);
        return builder;
      }
    
      public static final ThriftStructCodec<Args> CODEC = new ThriftStructCodec<Args>() {
        @Override
        public Args decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest startRequest = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 1: /* startRequest */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest startRequest_item;
                      startRequest_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest.decode(_iprot);
                      startRequest = startRequest_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setStartRequest(startRequest);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Args struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Args decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Args struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Args(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest startRequest) {
        this.startRequest = startRequest;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest getStartRequest() {
        return this.startRequest;
      }
  
      public boolean isSetStartRequest() {
        return this.isSetStartRequest;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetStartRequest) {
          if (startRequest != null) {
            _oprot.writeFieldBegin(StartRequestField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest startRequest_item = startRequest;
            startRequest_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Args)) {
          return false;
        }
        Args that = (Args) other;
        if (this.startRequest == null) {
          if (that.startRequest != null)
            return false;
        } else if (!this.startRequest.equals(that.startRequest)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Args(");
        if (this.isSetStartRequest) {
          sb.append("startRequest=");
          sb.append(this.startRequest);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.startRequest == null ? 0 : this.startRequest.hashCode());
        return hash;
      }
    }
  public static class Result implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("startToolJob_result");
      private static final TField SuccessField = new TField("success", TType.STRUCT, (short) 0);
      final Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success;
      boolean isSetSuccess;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse _success = null;
        private boolean isSetSuccess = false;
        public Builder setSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse value) {
          this._success = value;
          if (value != null) {
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public Builder setSuccessOpt(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> value) {
          if (value.isDefined()) {
            this._success = value.get();
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public boolean isSetSuccess() {
          return isSetSuccess;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
          return this._success;
        }
    
        public Builder unsetSuccess() {
          this._success = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptySuccess() {
          this._success = null;
          this.isSetSuccess = true;
          return this;
        }
    
    
        public Result build() {
          Result model = new Result(Option.fromNullable(this._success) );
          model.isSetSuccess = isSetSuccess;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (this.success.isDefined() && isSetSuccess) builder.setSuccess(this.success.get());
        return builder;
      }
    
      public static final ThriftStructCodec<Result> CODEC = new ThriftStructCodec<Result>() {
        @Override
        public Result decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 0: /* success */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item;
                      success_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse.decode(_iprot);
                      success = success_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setSuccess(success);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Result struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Result decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Result struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Result(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success) {
        this.success = success;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
        return this.success.get();
      }
        public Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> getSuccessOpt() {
        return this.success;
      }
  
      public boolean isSetSuccess() {
        return this.isSetSuccess;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetSuccess) {
          if (success.isDefined()) {
            _oprot.writeFieldBegin(SuccessField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item = success.get();
            success_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Result)) {
          return false;
        }
        Result that = (Result) other;
        if (this.success == null) {
          if (that.success != null)
            return false;
        } else if (!this.success.equals(that.success)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Result(");
        if (this.isSetSuccess) {
          sb.append("success=");
          sb.append(this.success);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.success.isDefined() ? this.success.get().hashCode() : 0);
        return hash;
      }
    }
  }
  public static class CreateSessionJob {
  public static class Args implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("createSessionJob_args");
      private static final TField CreateRequestField = new TField("createRequest", TType.STRUCT, (short) 1);
      final com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest createRequest;
      boolean isSetCreateRequest;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest _createRequest = null;
        private boolean isSetCreateRequest = false;
        public Builder setCreateRequest(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest value) {
          this._createRequest = value;
          if (value != null) {
            this.isSetCreateRequest = true;
          }
          return this;
        }
    
    
        public boolean isSetCreateRequest() {
          return isSetCreateRequest;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest getCreateRequest() {
          return this._createRequest;
        }
    
        public Builder unsetCreateRequest() {
          this._createRequest = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptyCreateRequest() {
          this._createRequest = null;
          this.isSetCreateRequest = true;
          return this;
        }
    
    
        public Args build() {
          Args model = new Args(this._createRequest);
          model.isSetCreateRequest = isSetCreateRequest;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (isSetCreateRequest) builder.setCreateRequest(this.createRequest);
        return builder;
      }
    
      public static final ThriftStructCodec<Args> CODEC = new ThriftStructCodec<Args>() {
        @Override
        public Args decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest createRequest = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 1: /* createRequest */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest createRequest_item;
                      createRequest_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest.decode(_iprot);
                      createRequest = createRequest_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setCreateRequest(createRequest);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Args struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Args decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Args struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Args(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest createRequest) {
        this.createRequest = createRequest;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest getCreateRequest() {
        return this.createRequest;
      }
  
      public boolean isSetCreateRequest() {
        return this.isSetCreateRequest;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetCreateRequest) {
          if (createRequest != null) {
            _oprot.writeFieldBegin(CreateRequestField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest createRequest_item = createRequest;
            createRequest_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Args)) {
          return false;
        }
        Args that = (Args) other;
        if (this.createRequest == null) {
          if (that.createRequest != null)
            return false;
        } else if (!this.createRequest.equals(that.createRequest)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Args(");
        if (this.isSetCreateRequest) {
          sb.append("createRequest=");
          sb.append(this.createRequest);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.createRequest == null ? 0 : this.createRequest.hashCode());
        return hash;
      }
    }
  public static class Result implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("createSessionJob_result");
      private static final TField SuccessField = new TField("success", TType.STRUCT, (short) 0);
      final Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success;
      boolean isSetSuccess;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse _success = null;
        private boolean isSetSuccess = false;
        public Builder setSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse value) {
          this._success = value;
          if (value != null) {
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public Builder setSuccessOpt(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> value) {
          if (value.isDefined()) {
            this._success = value.get();
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public boolean isSetSuccess() {
          return isSetSuccess;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
          return this._success;
        }
    
        public Builder unsetSuccess() {
          this._success = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptySuccess() {
          this._success = null;
          this.isSetSuccess = true;
          return this;
        }
    
    
        public Result build() {
          Result model = new Result(Option.fromNullable(this._success) );
          model.isSetSuccess = isSetSuccess;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (this.success.isDefined() && isSetSuccess) builder.setSuccess(this.success.get());
        return builder;
      }
    
      public static final ThriftStructCodec<Result> CODEC = new ThriftStructCodec<Result>() {
        @Override
        public Result decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 0: /* success */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item;
                      success_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse.decode(_iprot);
                      success = success_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setSuccess(success);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Result struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Result decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Result struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Result(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success) {
        this.success = success;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
        return this.success.get();
      }
        public Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> getSuccessOpt() {
        return this.success;
      }
  
      public boolean isSetSuccess() {
        return this.isSetSuccess;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetSuccess) {
          if (success.isDefined()) {
            _oprot.writeFieldBegin(SuccessField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item = success.get();
            success_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Result)) {
          return false;
        }
        Result that = (Result) other;
        if (this.success == null) {
          if (that.success != null)
            return false;
        } else if (!this.success.equals(that.success)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Result(");
        if (this.isSetSuccess) {
          sb.append("success=");
          sb.append(this.success);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.success.isDefined() ? this.success.get().hashCode() : 0);
        return hash;
      }
    }
  }
  public static class CreateAndStartSessionJob {
  public static class Args implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("createAndStartSessionJob_args");
      private static final TField StartRequestField = new TField("startRequest", TType.STRUCT, (short) 1);
      final com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest startRequest;
      boolean isSetStartRequest;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest _startRequest = null;
        private boolean isSetStartRequest = false;
        public Builder setStartRequest(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest value) {
          this._startRequest = value;
          if (value != null) {
            this.isSetStartRequest = true;
          }
          return this;
        }
    
    
        public boolean isSetStartRequest() {
          return isSetStartRequest;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest getStartRequest() {
          return this._startRequest;
        }
    
        public Builder unsetStartRequest() {
          this._startRequest = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptyStartRequest() {
          this._startRequest = null;
          this.isSetStartRequest = true;
          return this;
        }
    
    
        public Args build() {
          Args model = new Args(this._startRequest);
          model.isSetStartRequest = isSetStartRequest;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (isSetStartRequest) builder.setStartRequest(this.startRequest);
        return builder;
      }
    
      public static final ThriftStructCodec<Args> CODEC = new ThriftStructCodec<Args>() {
        @Override
        public Args decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest startRequest = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 1: /* startRequest */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest startRequest_item;
                      startRequest_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest.decode(_iprot);
                      startRequest = startRequest_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setStartRequest(startRequest);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Args struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Args decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Args struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Args(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest startRequest) {
        this.startRequest = startRequest;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest getStartRequest() {
        return this.startRequest;
      }
  
      public boolean isSetStartRequest() {
        return this.isSetStartRequest;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetStartRequest) {
          if (startRequest != null) {
            _oprot.writeFieldBegin(StartRequestField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest startRequest_item = startRequest;
            startRequest_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Args)) {
          return false;
        }
        Args that = (Args) other;
        if (this.startRequest == null) {
          if (that.startRequest != null)
            return false;
        } else if (!this.startRequest.equals(that.startRequest)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Args(");
        if (this.isSetStartRequest) {
          sb.append("startRequest=");
          sb.append(this.startRequest);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.startRequest == null ? 0 : this.startRequest.hashCode());
        return hash;
      }
    }
  public static class Result implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("createAndStartSessionJob_result");
      private static final TField SuccessField = new TField("success", TType.STRUCT, (short) 0);
      final Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success;
      boolean isSetSuccess;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse _success = null;
        private boolean isSetSuccess = false;
        public Builder setSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse value) {
          this._success = value;
          if (value != null) {
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public Builder setSuccessOpt(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> value) {
          if (value.isDefined()) {
            this._success = value.get();
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public boolean isSetSuccess() {
          return isSetSuccess;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
          return this._success;
        }
    
        public Builder unsetSuccess() {
          this._success = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptySuccess() {
          this._success = null;
          this.isSetSuccess = true;
          return this;
        }
    
    
        public Result build() {
          Result model = new Result(Option.fromNullable(this._success) );
          model.isSetSuccess = isSetSuccess;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (this.success.isDefined() && isSetSuccess) builder.setSuccess(this.success.get());
        return builder;
      }
    
      public static final ThriftStructCodec<Result> CODEC = new ThriftStructCodec<Result>() {
        @Override
        public Result decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 0: /* success */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item;
                      success_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse.decode(_iprot);
                      success = success_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setSuccess(success);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Result struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Result decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Result struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Result(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success) {
        this.success = success;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
        return this.success.get();
      }
        public Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> getSuccessOpt() {
        return this.success;
      }
  
      public boolean isSetSuccess() {
        return this.isSetSuccess;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetSuccess) {
          if (success.isDefined()) {
            _oprot.writeFieldBegin(SuccessField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item = success.get();
            success_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Result)) {
          return false;
        }
        Result that = (Result) other;
        if (this.success == null) {
          if (that.success != null)
            return false;
        } else if (!this.success.equals(that.success)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Result(");
        if (this.isSetSuccess) {
          sb.append("success=");
          sb.append(this.success);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.success.isDefined() ? this.success.get().hashCode() : 0);
        return hash;
      }
    }
  }
  public static class CreateAndStartToolJob {
  public static class Args implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("createAndStartToolJob_args");
      private static final TField StartRequestField = new TField("startRequest", TType.STRUCT, (short) 1);
      final com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest startRequest;
      boolean isSetStartRequest;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest _startRequest = null;
        private boolean isSetStartRequest = false;
        public Builder setStartRequest(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest value) {
          this._startRequest = value;
          if (value != null) {
            this.isSetStartRequest = true;
          }
          return this;
        }
    
    
        public boolean isSetStartRequest() {
          return isSetStartRequest;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest getStartRequest() {
          return this._startRequest;
        }
    
        public Builder unsetStartRequest() {
          this._startRequest = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptyStartRequest() {
          this._startRequest = null;
          this.isSetStartRequest = true;
          return this;
        }
    
    
        public Args build() {
          Args model = new Args(this._startRequest);
          model.isSetStartRequest = isSetStartRequest;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (isSetStartRequest) builder.setStartRequest(this.startRequest);
        return builder;
      }
    
      public static final ThriftStructCodec<Args> CODEC = new ThriftStructCodec<Args>() {
        @Override
        public Args decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest startRequest = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 1: /* startRequest */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest startRequest_item;
                      startRequest_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest.decode(_iprot);
                      startRequest = startRequest_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setStartRequest(startRequest);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Args struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Args decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Args struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Args(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest startRequest) {
        this.startRequest = startRequest;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest getStartRequest() {
        return this.startRequest;
      }
  
      public boolean isSetStartRequest() {
        return this.isSetStartRequest;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetStartRequest) {
          if (startRequest != null) {
            _oprot.writeFieldBegin(StartRequestField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest startRequest_item = startRequest;
            startRequest_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Args)) {
          return false;
        }
        Args that = (Args) other;
        if (this.startRequest == null) {
          if (that.startRequest != null)
            return false;
        } else if (!this.startRequest.equals(that.startRequest)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Args(");
        if (this.isSetStartRequest) {
          sb.append("startRequest=");
          sb.append(this.startRequest);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.startRequest == null ? 0 : this.startRequest.hashCode());
        return hash;
      }
    }
  public static class Result implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("createAndStartToolJob_result");
      private static final TField SuccessField = new TField("success", TType.STRUCT, (short) 0);
      final Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success;
      boolean isSetSuccess;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse _success = null;
        private boolean isSetSuccess = false;
        public Builder setSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse value) {
          this._success = value;
          if (value != null) {
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public Builder setSuccessOpt(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> value) {
          if (value.isDefined()) {
            this._success = value.get();
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public boolean isSetSuccess() {
          return isSetSuccess;
        }
    
        public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
          return this._success;
        }
    
        public Builder unsetSuccess() {
          this._success = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptySuccess() {
          this._success = null;
          this.isSetSuccess = true;
          return this;
        }
    
    
        public Result build() {
          Result model = new Result(Option.fromNullable(this._success) );
          model.isSetSuccess = isSetSuccess;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (this.success.isDefined() && isSetSuccess) builder.setSuccess(this.success.get());
        return builder;
      }
    
      public static final ThriftStructCodec<Result> CODEC = new ThriftStructCodec<Result>() {
        @Override
        public Result decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 0: /* success */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item;
                      success_item = com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse.decode(_iprot);
                      success = success_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setSuccess(success);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Result struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Result decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Result struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Result(Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> success) {
        this.success = success;
      }
    
    
      public com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse getSuccess() {
        return this.success.get();
      }
        public Option<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> getSuccessOpt() {
        return this.success;
      }
  
      public boolean isSetSuccess() {
        return this.isSetSuccess;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetSuccess) {
          if (success.isDefined()) {
            _oprot.writeFieldBegin(SuccessField);
            com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse success_item = success.get();
            success_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Result)) {
          return false;
        }
        Result that = (Result) other;
        if (this.success == null) {
          if (that.success != null)
            return false;
        } else if (!this.success.equals(that.success)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Result(");
        if (this.isSetSuccess) {
          sb.append("success=");
          sb.append(this.success);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.success.isDefined() ? this.success.get().hashCode() : 0);
        return hash;
      }
    }
  }


  public static class Processor extends TBaseProcessor<Iface> implements TProcessor {
    private static final Logger LOG = LoggerFactory.getLogger(Processor.class);
    public Processor(Iface iface) {
      super(iface, getProcessMap(new HashMap<String, TProcessFunction<Iface, ? extends ThriftStruct>>()));
    }
  
    public Processor(Iface iface, Map<String, TProcessFunction<Iface, ? extends ThriftStruct>> processMap) {
      super(iface, getProcessMap(processMap));
    }
    
    private static <I extends Iface> Map<String,  TProcessFunction<Iface, ? extends ThriftStruct>> getProcessMap(Map<String, TProcessFunction<Iface, ? extends ThriftStruct>> processMap) {
      processMap.put("startSessionJob", new StartSessionJobFunction());
      processMap.put("startToolJob", new StartToolJobFunction());
      processMap.put("createSessionJob", new CreateSessionJobFunction());
      processMap.put("createAndStartSessionJob", new CreateAndStartSessionJobFunction());
      processMap.put("createAndStartToolJob", new CreateAndStartToolJobFunction());
      return processMap;
    }
    private static class StartSessionJobFunction extends TProcessFunction<Iface, StartSessionJob.Args> {
      public StartSessionJobFunction() {
        super("startSessionJob");
      }
    
      @Override
      protected boolean isOneway() {
        return false;
      }
    
      @Override
      public ThriftStruct getResult(Iface iface, StartSessionJob.Args args) throws TException {
        StartSessionJob.Result.Builder builder = new StartSessionJob.Result.Builder();
        com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse result = iface.startSessionJob(args.startRequest);
        builder.setSuccess(result);
        return builder.build();
      }
    }
    private static class StartToolJobFunction extends TProcessFunction<Iface, StartToolJob.Args> {
      public StartToolJobFunction() {
        super("startToolJob");
      }
    
      @Override
      protected boolean isOneway() {
        return false;
      }
    
      @Override
      public ThriftStruct getResult(Iface iface, StartToolJob.Args args) throws TException {
        StartToolJob.Result.Builder builder = new StartToolJob.Result.Builder();
        com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse result = iface.startToolJob(args.startRequest);
        builder.setSuccess(result);
        return builder.build();
      }
    }
    private static class CreateSessionJobFunction extends TProcessFunction<Iface, CreateSessionJob.Args> {
      public CreateSessionJobFunction() {
        super("createSessionJob");
      }
    
      @Override
      protected boolean isOneway() {
        return false;
      }
    
      @Override
      public ThriftStruct getResult(Iface iface, CreateSessionJob.Args args) throws TException {
        CreateSessionJob.Result.Builder builder = new CreateSessionJob.Result.Builder();
        com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse result = iface.createSessionJob(args.createRequest);
        builder.setSuccess(result);
        return builder.build();
      }
    }
    private static class CreateAndStartSessionJobFunction extends TProcessFunction<Iface, CreateAndStartSessionJob.Args> {
      public CreateAndStartSessionJobFunction() {
        super("createAndStartSessionJob");
      }
    
      @Override
      protected boolean isOneway() {
        return false;
      }
    
      @Override
      public ThriftStruct getResult(Iface iface, CreateAndStartSessionJob.Args args) throws TException {
        CreateAndStartSessionJob.Result.Builder builder = new CreateAndStartSessionJob.Result.Builder();
        com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse result = iface.createAndStartSessionJob(args.startRequest);
        builder.setSuccess(result);
        return builder.build();
      }
    }
    private static class CreateAndStartToolJobFunction extends TProcessFunction<Iface, CreateAndStartToolJob.Args> {
      public CreateAndStartToolJobFunction() {
        super("createAndStartToolJob");
      }
    
      @Override
      protected boolean isOneway() {
        return false;
      }
    
      @Override
      public ThriftStruct getResult(Iface iface, CreateAndStartToolJob.Args args) throws TException {
        CreateAndStartToolJob.Result.Builder builder = new CreateAndStartToolJob.Result.Builder();
        com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse result = iface.createAndStartToolJob(args.startRequest);
        builder.setSuccess(result);
        return builder.build();
      }
    }
  }
  
  
  public static class FutureClient extends TFutureClient implements FutureIface {
    private static final Logger LOG = LoggerFactory.getLogger(FutureClient.class);

    public FutureClient(Client client) {
      super(client);
    }
    
    @Override
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> startSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest startRequest) {
      StartSessionJob.Args.Builder builder = new StartSessionJob.Args.Builder();
      builder.setStartRequest(startRequest);
      StartSessionJob.Args args = builder.build();
      ThriftMessage request = encodeRequest("startSessionJob", args);
      if (LOG.isDebugEnabled()) {
        LOG.debug("send thrift message {}",request);
      }
      Future<Message> responseFuture = client.send(request);
      final SettableFuture<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> settableFuture = Futures.newSettableFuture(request);
      Futures.addCallback(responseFuture, new FutureCallback<Message>() {
    
        @Override
        public void onSuccess(Message response) {
          StartSessionJob.Result result = null;
          try {
            ThriftMessage thriftMessage = (ThriftMessage) response;
            if (LOG.isDebugEnabled()) {
              LOG.debug("receive thrift message {}", thriftMessage);
            }
            result = decodeResponse(thriftMessage);
          } catch (Throwable e) {
            settableFuture.setException(e);
            return;
          }
          if (result.isSetSuccess()) {
            settableFuture.set(result.getSuccess());
            return;
          }
          settableFuture.set(null);
        }
    
        @Override
        public void onFailure(Throwable t) {
          settableFuture.setException(t);
        }
      });
      return settableFuture;
    }
    
    @Override
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> startToolJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest startRequest) {
      StartToolJob.Args.Builder builder = new StartToolJob.Args.Builder();
      builder.setStartRequest(startRequest);
      StartToolJob.Args args = builder.build();
      ThriftMessage request = encodeRequest("startToolJob", args);
      if (LOG.isDebugEnabled()) {
        LOG.debug("send thrift message {}",request);
      }
      Future<Message> responseFuture = client.send(request);
      final SettableFuture<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> settableFuture = Futures.newSettableFuture(request);
      Futures.addCallback(responseFuture, new FutureCallback<Message>() {
    
        @Override
        public void onSuccess(Message response) {
          StartToolJob.Result result = null;
          try {
            ThriftMessage thriftMessage = (ThriftMessage) response;
            if (LOG.isDebugEnabled()) {
              LOG.debug("receive thrift message {}", thriftMessage);
            }
            result = decodeResponse(thriftMessage);
          } catch (Throwable e) {
            settableFuture.setException(e);
            return;
          }
          if (result.isSetSuccess()) {
            settableFuture.set(result.getSuccess());
            return;
          }
          settableFuture.set(null);
        }
    
        @Override
        public void onFailure(Throwable t) {
          settableFuture.setException(t);
        }
      });
      return settableFuture;
    }
    
    @Override
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> createSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest createRequest) {
      CreateSessionJob.Args.Builder builder = new CreateSessionJob.Args.Builder();
      builder.setCreateRequest(createRequest);
      CreateSessionJob.Args args = builder.build();
      ThriftMessage request = encodeRequest("createSessionJob", args);
      if (LOG.isDebugEnabled()) {
        LOG.debug("send thrift message {}",request);
      }
      Future<Message> responseFuture = client.send(request);
      final SettableFuture<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> settableFuture = Futures.newSettableFuture(request);
      Futures.addCallback(responseFuture, new FutureCallback<Message>() {
    
        @Override
        public void onSuccess(Message response) {
          CreateSessionJob.Result result = null;
          try {
            ThriftMessage thriftMessage = (ThriftMessage) response;
            if (LOG.isDebugEnabled()) {
              LOG.debug("receive thrift message {}", thriftMessage);
            }
            result = decodeResponse(thriftMessage);
          } catch (Throwable e) {
            settableFuture.setException(e);
            return;
          }
          if (result.isSetSuccess()) {
            settableFuture.set(result.getSuccess());
            return;
          }
          settableFuture.set(null);
        }
    
        @Override
        public void onFailure(Throwable t) {
          settableFuture.setException(t);
        }
      });
      return settableFuture;
    }
    
    @Override
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> createAndStartSessionJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest startRequest) {
      CreateAndStartSessionJob.Args.Builder builder = new CreateAndStartSessionJob.Args.Builder();
      builder.setStartRequest(startRequest);
      CreateAndStartSessionJob.Args args = builder.build();
      ThriftMessage request = encodeRequest("createAndStartSessionJob", args);
      if (LOG.isDebugEnabled()) {
        LOG.debug("send thrift message {}",request);
      }
      Future<Message> responseFuture = client.send(request);
      final SettableFuture<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> settableFuture = Futures.newSettableFuture(request);
      Futures.addCallback(responseFuture, new FutureCallback<Message>() {
    
        @Override
        public void onSuccess(Message response) {
          CreateAndStartSessionJob.Result result = null;
          try {
            ThriftMessage thriftMessage = (ThriftMessage) response;
            if (LOG.isDebugEnabled()) {
              LOG.debug("receive thrift message {}", thriftMessage);
            }
            result = decodeResponse(thriftMessage);
          } catch (Throwable e) {
            settableFuture.setException(e);
            return;
          }
          if (result.isSetSuccess()) {
            settableFuture.set(result.getSuccess());
            return;
          }
          settableFuture.set(null);
        }
    
        @Override
        public void onFailure(Throwable t) {
          settableFuture.setException(t);
        }
      });
      return settableFuture;
    }
    
    @Override
    public Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> createAndStartToolJob(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest startRequest) {
      CreateAndStartToolJob.Args.Builder builder = new CreateAndStartToolJob.Args.Builder();
      builder.setStartRequest(startRequest);
      CreateAndStartToolJob.Args args = builder.build();
      ThriftMessage request = encodeRequest("createAndStartToolJob", args);
      if (LOG.isDebugEnabled()) {
        LOG.debug("send thrift message {}",request);
      }
      Future<Message> responseFuture = client.send(request);
      final SettableFuture<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> settableFuture = Futures.newSettableFuture(request);
      Futures.addCallback(responseFuture, new FutureCallback<Message>() {
    
        @Override
        public void onSuccess(Message response) {
          CreateAndStartToolJob.Result result = null;
          try {
            ThriftMessage thriftMessage = (ThriftMessage) response;
            if (LOG.isDebugEnabled()) {
              LOG.debug("receive thrift message {}", thriftMessage);
            }
            result = decodeResponse(thriftMessage);
          } catch (Throwable e) {
            settableFuture.setException(e);
            return;
          }
          if (result.isSetSuccess()) {
            settableFuture.set(result.getSuccess());
            return;
          }
          settableFuture.set(null);
        }
    
        @Override
        public void onFailure(Throwable t) {
          settableFuture.setException(t);
        }
      });
      return settableFuture;
    }
  }
  
  public static class ArgCodec extends TCodec {
    
    public ArgCodec(){
      super(getArgsCodecMap(new HashMap<String, ThriftStructCodec>()));
    }
    
    public static final Map<String, ThriftStructCodec> getArgsCodecMap(Map<String, ThriftStructCodec> codecMap) {
      codecMap.put("startSessionJob", StartSessionJob.Args.CODEC);
      codecMap.put("startToolJob", StartToolJob.Args.CODEC);
      codecMap.put("createSessionJob", CreateSessionJob.Args.CODEC);
      codecMap.put("createAndStartSessionJob", CreateAndStartSessionJob.Args.CODEC);
      codecMap.put("createAndStartToolJob", CreateAndStartToolJob.Args.CODEC);
      return codecMap;
    }
  }
  public static class ResultCodec extends TCodec {
    
    public ResultCodec(){
      super(getResultCodecMap(new HashMap<String, ThriftStructCodec>()));
    }
    
    public static final Map<String, ThriftStructCodec> getResultCodecMap(Map<String, ThriftStructCodec> codecMap) {
      codecMap.put("startSessionJob", StartSessionJob.Result.CODEC);
      codecMap.put("startToolJob", StartToolJob.Result.CODEC);
      codecMap.put("createSessionJob", CreateSessionJob.Result.CODEC);
      codecMap.put("createAndStartSessionJob", CreateAndStartSessionJob.Result.CODEC);
      codecMap.put("createAndStartToolJob", CreateAndStartToolJob.Result.CODEC);
      return codecMap;
    }
  }
}