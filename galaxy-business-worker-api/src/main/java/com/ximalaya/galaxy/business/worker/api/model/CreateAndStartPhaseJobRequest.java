/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.model;

import java.nio.ByteBuffer;
import java.util.*;


public class CreateAndStartPhaseJobRequest {

  private Long uid;
  private Long sessionCode;
  private String phaseName;
  private String systemPrompt;
  private String prompt;

  public CreateAndStartPhaseJobRequest() {
  }

  public CreateAndStartPhaseJobRequest(Long uid, Long sessionCode, String phaseName, String systemPrompt, String prompt) {
    this.uid = uid;
    this.sessionCode = sessionCode;
    this.phaseName = phaseName;
    this.systemPrompt = systemPrompt;
    this.prompt = prompt;
  }

  public void setUid(Long uid) {
    this.uid = uid;
  }

  public Long getUid() {
    return this.uid;
  }

  public void setSessionCode(Long sessionCode) {
    this.sessionCode = sessionCode;
  }

  public Long getSessionCode() {
    return this.sessionCode;
  }

  public void setPhaseName(String phaseName) {
    this.phaseName = phaseName;
  }

  public String getPhaseName() {
    return this.phaseName;
  }

  public void setSystemPrompt(String systemPrompt) {
    this.systemPrompt = systemPrompt;
  }

  public String getSystemPrompt() {
    return this.systemPrompt;
  }

  public void setPrompt(String prompt) {
    this.prompt = prompt;
  }

  public String getPrompt() {
    return this.prompt;
  }

  @Override
  public String toString() {
    return "CreateAndStartPhaseJobRequest(" +"uid=" + this.uid + ", " + "sessionCode=" + this.sessionCode + ", " + "phaseName=" + this.phaseName + ", " + "systemPrompt=" + this.systemPrompt + ", " + "prompt=" + this.prompt + ")";
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + (this.uid == null ? 0 : this.uid.hashCode());
    hash = 31 * hash + (this.sessionCode == null ? 0 : this.sessionCode.hashCode());
    hash = 31 * hash + (this.phaseName == null ? 0 : this.phaseName.hashCode());
    hash = 31 * hash + (this.systemPrompt == null ? 0 : this.systemPrompt.hashCode());
    hash = 31 * hash + (this.prompt == null ? 0 : this.prompt.hashCode());
    return hash;
  }

  @Override
  public boolean equals(Object other) {
    if (!(other instanceof CreateAndStartPhaseJobRequest)) {
      return false;
    }
    CreateAndStartPhaseJobRequest that = (CreateAndStartPhaseJobRequest) other;
    if (this.uid == null) {
      if (that.uid != null)
        return false;
    } else if (!this.uid.equals(that.uid)) {
      return false;
    }
    if (this.sessionCode == null) {
      if (that.sessionCode != null)
        return false;
    } else if (!this.sessionCode.equals(that.sessionCode)) {
      return false;
    }
    if (this.phaseName == null) {
      if (that.phaseName != null)
        return false;
    } else if (!this.phaseName.equals(that.phaseName)) {
      return false;
    }
    if (this.systemPrompt == null) {
      if (that.systemPrompt != null)
        return false;
    } else if (!this.systemPrompt.equals(that.systemPrompt)) {
      return false;
    }
    if (this.prompt == null) {
      if (that.prompt != null)
        return false;
    } else if (!this.prompt.equals(that.prompt)) {
      return false;
    }
    return true;
  }
}