/**
 * Generated by Dal<PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.converter;

import java.nio.ByteBuffer;
import java.util.*;


public class CreateSessionJobRequestConverter {

  public static com.ximalaya.galaxy.business.worker.api.model.CreateSessionJobRequest transform(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.model.CreateSessionJobRequest target = new com.ximalaya.galaxy.business.worker.api.model.CreateSessionJobRequest();
    if (null != source) {
      if (source.isSetUid()) {
        Long _uid_item = source.getUid();
        target.setUid(_uid_item);
      }
      if (source.isSetPhaseName()) {
        String _phaseName_item = source.getPhaseName();
        target.setPhaseName(_phaseName_item);
      }
      if (source.isSetSystemPrompt()) {
        String _systemPrompt_item = source.getSystemPrompt();
        target.setSystemPrompt(_systemPrompt_item);
      }
      if (source.isSetPrompt()) {
        String _prompt_item = source.getPrompt();
        target.setPrompt(_prompt_item);
      }
      if (source.isSetBusinessData()) {
        String _businessData_item = source.getBusinessData();
        target.setBusinessData(_businessData_item);
      }
    }
    return target;
  }

  public static com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest transform(com.ximalaya.galaxy.business.worker.api.model.CreateSessionJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest.Builder target = new com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest.Builder();
    if (null != source) {
      if (source.getUid() != null) {
        Long _uid_item = source.getUid();
        target.setUid(_uid_item);
      }
      if (source.getPhaseName() != null) {
        String _phaseName_item = source.getPhaseName();
        target.setPhaseName(_phaseName_item);
      }
      if (source.getSystemPrompt() != null) {
        String _systemPrompt_item = source.getSystemPrompt();
        target.setSystemPrompt(_systemPrompt_item);
      }
      if (source.getPrompt() != null) {
        String _prompt_item = source.getPrompt();
        target.setPrompt(_prompt_item);
      }
      if (source.getBusinessData() != null) {
        String _businessData_item = source.getBusinessData();
        target.setBusinessData(_businessData_item);
      }
    }
    return target.build();
  }
}