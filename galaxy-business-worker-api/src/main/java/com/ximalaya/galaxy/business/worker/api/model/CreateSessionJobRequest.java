/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.model;

import java.nio.ByteBuffer;
import java.util.*;


public class CreateSessionJobRequest {

  private Long uid;
  private String phaseName;
  private String systemPrompt;
  private String prompt;
  private String businessData;

  public CreateSessionJobRequest() {
  }

  public CreateSessionJobRequest(Long uid, String phaseName, String systemPrompt, String prompt, String businessData) {
    this.uid = uid;
    this.phaseName = phaseName;
    this.systemPrompt = systemPrompt;
    this.prompt = prompt;
    this.businessData = businessData;
  }

  public void setUid(Long uid) {
    this.uid = uid;
  }

  public Long getUid() {
    return this.uid;
  }

  public void setPhaseName(String phaseName) {
    this.phaseName = phaseName;
  }

  public String getPhaseName() {
    return this.phaseName;
  }

  public void setSystemPrompt(String systemPrompt) {
    this.systemPrompt = systemPrompt;
  }

  public String getSystemPrompt() {
    return this.systemPrompt;
  }

  public void setPrompt(String prompt) {
    this.prompt = prompt;
  }

  public String getPrompt() {
    return this.prompt;
  }

  public void setBusinessData(String businessData) {
    this.businessData = businessData;
  }

  public String getBusinessData() {
    return this.businessData;
  }

  @Override
  public String toString() {
    return "CreateSessionJobRequest(" +"uid=" + this.uid + ", " + "phaseName=" + this.phaseName + ", " + "systemPrompt=" + this.systemPrompt + ", " + "prompt=" + this.prompt + ", " + "businessData=" + this.businessData + ")";
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + (this.uid == null ? 0 : this.uid.hashCode());
    hash = 31 * hash + (this.phaseName == null ? 0 : this.phaseName.hashCode());
    hash = 31 * hash + (this.systemPrompt == null ? 0 : this.systemPrompt.hashCode());
    hash = 31 * hash + (this.prompt == null ? 0 : this.prompt.hashCode());
    hash = 31 * hash + (this.businessData == null ? 0 : this.businessData.hashCode());
    return hash;
  }

  @Override
  public boolean equals(Object other) {
    if (!(other instanceof CreateSessionJobRequest)) {
      return false;
    }
    CreateSessionJobRequest that = (CreateSessionJobRequest) other;
    if (this.uid == null) {
      if (that.uid != null)
        return false;
    } else if (!this.uid.equals(that.uid)) {
      return false;
    }
    if (this.phaseName == null) {
      if (that.phaseName != null)
        return false;
    } else if (!this.phaseName.equals(that.phaseName)) {
      return false;
    }
    if (this.systemPrompt == null) {
      if (that.systemPrompt != null)
        return false;
    } else if (!this.systemPrompt.equals(that.systemPrompt)) {
      return false;
    }
    if (this.prompt == null) {
      if (that.prompt != null)
        return false;
    } else if (!this.prompt.equals(that.prompt)) {
      return false;
    }
    if (this.businessData == null) {
      if (that.businessData != null)
        return false;
    } else if (!this.businessData.equals(that.businessData)) {
      return false;
    }
    return true;
  }
}