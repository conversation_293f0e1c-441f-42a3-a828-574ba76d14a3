/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.service.impl;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import com.ximalaya.mainstay.common.concurrent.FutureCallback;
import com.ximalaya.mainstay.common.concurrent.Futures;
import com.ximalaya.mainstay.common.concurrent.Future;
import com.ximalaya.mainstay.common.concurrent.SettableFuture;
import com.ximalaya.mainstay.rpc.thrift.TException;

import com.ximalaya.galaxy.business.worker.api.service.*;
import com.ximalaya.galaxy.business.worker.api.ex.*;


public class ThriftRemoteAsyncGalaxyBusinessWorkerPingService implements IRemoteAsyncGalaxyBusinessWorkerPingService {

  @Autowired
  private com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerPingService.FutureIface client;

  
  @Override
  public Future<com.ximalaya.galaxy.business.worker.api.model.PongResponse> ping(com.ximalaya.galaxy.business.worker.api.model.PingRequest pingRequest) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.PingRequest _pingRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.PingRequestConverter.transform(pingRequest);
  
    Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.PongResponse> client_result = client.ping(_pingRequest_arg_item);
    final SettableFuture<com.ximalaya.galaxy.business.worker.api.model.PongResponse> settableFuture = Futures.newSettableFuture();
    Futures.addCallback(client_result, new FutureCallback<com.ximalaya.galaxy.business.worker.api.thrift.entity.PongResponse>() {
  
      @Override
      public void onSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.PongResponse client_result) {
        if (client_result == null) {
          settableFuture.set(null);
          return;
        }
        com.ximalaya.galaxy.business.worker.api.model.PongResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.PongResponseConverter.transform(client_result);
        settableFuture.set(_client_result_result_item);
  
  
      }
  
      @Override
      public void onFailure(Throwable t) {
        settableFuture.setException(t);
      }
    });
    return settableFuture;
  }
}