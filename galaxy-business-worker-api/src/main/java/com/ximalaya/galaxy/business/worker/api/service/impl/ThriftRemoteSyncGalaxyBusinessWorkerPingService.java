/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.service.impl;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.lang.RuntimeException;

import org.springframework.beans.factory.annotation.Autowired;

import com.ximalaya.mainstay.rpc.thrift.TException;

import com.ximalaya.galaxy.business.worker.api.service.*;
import com.ximalaya.galaxy.business.worker.api.ex.*;


public class ThriftRemoteSyncGalaxyBusinessWorkerPingService implements IRemoteSyncGalaxyBusinessWorkerPingService {

  @Autowired
  private com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerPingService.Iface client;

  
  @Override
  public com.ximalaya.galaxy.business.worker.api.model.PongResponse ping(com.ximalaya.galaxy.business.worker.api.model.PingRequest pingRequest) {
    try {
      com.ximalaya.galaxy.business.worker.api.thrift.entity.PingRequest _pingRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.PingRequestConverter.transform(pingRequest);
  
      com.ximalaya.galaxy.business.worker.api.thrift.entity.PongResponse client_result = client.ping(_pingRequest_arg_item);
      if (client_result == null) {
        return null;
      }
  
      com.ximalaya.galaxy.business.worker.api.model.PongResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.PongResponseConverter.transform(client_result);
      return _client_result_result_item;
    } catch (TException e) {
      throw new GalaxyBusinessWorkerPingServiceException("call ping failed.", e);
    }
  }
}