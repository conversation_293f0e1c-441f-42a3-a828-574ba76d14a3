/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.converter;

import java.nio.ByteBuffer;
import java.util.*;


public class CreateAndStartToolJobRequestConverter {

  public static com.ximalaya.galaxy.business.worker.api.model.CreateAndStartToolJobRequest transform(com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.model.CreateAndStartToolJobRequest target = new com.ximalaya.galaxy.business.worker.api.model.CreateAndStartToolJobRequest();
    if (null != source) {
      if (source.isSetUid()) {
        Long _uid_item = source.getUid();
        target.setUid(_uid_item);
      }
      if (source.isSetSessionCode()) {
        Long _sessionCode_item = source.getSessionCode();
        target.setSessionCode(_sessionCode_item);
      }
      if (source.isSetPhaseName()) {
        String _phaseName_item = source.getPhaseName();
        target.setPhaseName(_phaseName_item);
      }
      if (source.isSetToolId()) {
        Long _toolId_item = source.getToolId();
        target.setToolId(_toolId_item);
      }
      if (source.isSetArgs()) {
        String _args_item = source.getArgs();
        target.setArgs(_args_item);
      }
      if (source.isSetNeedRemoveBlockCodes()) {
      List<Long> _needRemoveBlockCodes_item = new ArrayList<Long>(source.getNeedRemoveBlockCodes().size());
      
      for (Long needRemoveBlockCodes_item_element : source.getNeedRemoveBlockCodes()) {
        Long _needRemoveBlockCodes_item_element = needRemoveBlockCodes_item_element;
        _needRemoveBlockCodes_item.add(_needRemoveBlockCodes_item_element);
      }
        target.setNeedRemoveBlockCodes(_needRemoveBlockCodes_item);
      }
    }
    return target;
  }

  public static com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest transform(com.ximalaya.galaxy.business.worker.api.model.CreateAndStartToolJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest.Builder target = new com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest.Builder();
    if (null != source) {
      if (source.getUid() != null) {
        Long _uid_item = source.getUid();
        target.setUid(_uid_item);
      }
      if (source.getSessionCode() != null) {
        Long _sessionCode_item = source.getSessionCode();
        target.setSessionCode(_sessionCode_item);
      }
      if (source.getPhaseName() != null) {
        String _phaseName_item = source.getPhaseName();
        target.setPhaseName(_phaseName_item);
      }
      if (source.getToolId() != null) {
        Long _toolId_item = source.getToolId();
        target.setToolId(_toolId_item);
      }
      if (source.getArgs() != null) {
        String _args_item = source.getArgs();
        target.setArgs(_args_item);
      }
      if (source.getNeedRemoveBlockCodes() != null) {
      List<Long> _needRemoveBlockCodes_item = new ArrayList<Long>(source.getNeedRemoveBlockCodes().size());
      
      for (Long needRemoveBlockCodes_item_element : source.getNeedRemoveBlockCodes()) {
        Long _needRemoveBlockCodes_item_element = needRemoveBlockCodes_item_element;
        _needRemoveBlockCodes_item.add(_needRemoveBlockCodes_item_element);
      }
        target.setNeedRemoveBlockCodes(_needRemoveBlockCodes_item);
      }
 
    }
    return target.build();
  }
}