/**
 * Generated by Dal<PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.service.impl;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import com.ximalaya.mainstay.common.concurrent.FutureCallback;
import com.ximalaya.mainstay.common.concurrent.Futures;
import com.ximalaya.mainstay.common.concurrent.Future;
import com.ximalaya.mainstay.common.concurrent.SettableFuture;
import com.ximalaya.mainstay.rpc.thrift.TException;

import com.ximalaya.galaxy.business.worker.api.service.*;
import com.ximalaya.galaxy.business.worker.api.ex.*;


public class ThriftRemoteAsyncGalaxyBusinessWorkerJobService implements IRemoteAsyncGalaxyBusinessWorkerJobService {

  @Autowired
  private com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerJobService.FutureIface client;

  
  @Override
  public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> startSessionJob(com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest startRequest) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest _startRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.StartSessionJobRequestConverter.transform(startRequest);
  
    Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> client_result = client.startSessionJob(_startRequest_arg_item);
    final SettableFuture<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> settableFuture = Futures.newSettableFuture();
    Futures.addCallback(client_result, new FutureCallback<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse>() {
  
      @Override
      public void onSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse client_result) {
        if (client_result == null) {
          settableFuture.set(null);
          return;
        }
        com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter.transform(client_result);
        settableFuture.set(_client_result_result_item);
  
  
      }
  
      @Override
      public void onFailure(Throwable t) {
        settableFuture.setException(t);
      }
    });
    return settableFuture;
  }
  
  @Override
  public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> startToolJob(com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest startRequest) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest _startRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.StartToolJobRequestConverter.transform(startRequest);
  
    Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> client_result = client.startToolJob(_startRequest_arg_item);
    final SettableFuture<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> settableFuture = Futures.newSettableFuture();
    Futures.addCallback(client_result, new FutureCallback<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse>() {
  
      @Override
      public void onSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse client_result) {
        if (client_result == null) {
          settableFuture.set(null);
          return;
        }
        com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter.transform(client_result);
        settableFuture.set(_client_result_result_item);
  
  
      }
  
      @Override
      public void onFailure(Throwable t) {
        settableFuture.setException(t);
      }
    });
    return settableFuture;
  }
  
  @Override
  public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> createSessionJob(com.ximalaya.galaxy.business.worker.api.model.CreateSessionJobRequest createRequest) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateSessionJobRequest _createRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.CreateSessionJobRequestConverter.transform(createRequest);
  
    Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> client_result = client.createSessionJob(_createRequest_arg_item);
    final SettableFuture<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> settableFuture = Futures.newSettableFuture();
    Futures.addCallback(client_result, new FutureCallback<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse>() {
  
      @Override
      public void onSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse client_result) {
        if (client_result == null) {
          settableFuture.set(null);
          return;
        }
        com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter.transform(client_result);
        settableFuture.set(_client_result_result_item);
  
  
      }
  
      @Override
      public void onFailure(Throwable t) {
        settableFuture.setException(t);
      }
    });
    return settableFuture;
  }
  
  @Override
  public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> createAndStartSessionJob(com.ximalaya.galaxy.business.worker.api.model.CreateAndStartPhaseJobRequest startRequest) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartPhaseJobRequest _startRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.CreateAndStartPhaseJobRequestConverter.transform(startRequest);
  
    Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> client_result = client.createAndStartSessionJob(_startRequest_arg_item);
    final SettableFuture<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> settableFuture = Futures.newSettableFuture();
    Futures.addCallback(client_result, new FutureCallback<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse>() {
  
      @Override
      public void onSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse client_result) {
        if (client_result == null) {
          settableFuture.set(null);
          return;
        }
        com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter.transform(client_result);
        settableFuture.set(_client_result_result_item);
  
  
      }
  
      @Override
      public void onFailure(Throwable t) {
        settableFuture.setException(t);
      }
    });
    return settableFuture;
  }
  
  @Override
  public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> createAndStartToolJob(com.ximalaya.galaxy.business.worker.api.model.CreateAndStartToolJobRequest startRequest) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.CreateAndStartToolJobRequest _startRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.CreateAndStartToolJobRequestConverter.transform(startRequest);
  
    Future<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse> client_result = client.createAndStartToolJob(_startRequest_arg_item);
    final SettableFuture<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> settableFuture = Futures.newSettableFuture();
    Futures.addCallback(client_result, new FutureCallback<com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse>() {
  
      @Override
      public void onSuccess(com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse client_result) {
        if (client_result == null) {
          settableFuture.set(null);
          return;
        }
        com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter.transform(client_result);
        settableFuture.set(_client_result_result_item);
  
  
      }
  
      @Override
      public void onFailure(Throwable t) {
        settableFuture.setException(t);
      }
    });
    return settableFuture;
  }
}