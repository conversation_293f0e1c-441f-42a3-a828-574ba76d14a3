/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.service;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.ximalaya.mainstay.common.RouteArg;
import com.ximalaya.mainstay.common.concurrent.Future;


public interface IRemoteAsyncGalaxyBusinessWorkerJobService {
    
    public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> startSessionJob(com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest startRequest);
    
    public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> startToolJob(com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest startRequest);
    
    public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> createSessionJob(com.ximalaya.galaxy.business.worker.api.model.CreateSessionJobRequest createRequest);
    
    public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> createAndStartSessionJob(com.ximalaya.galaxy.business.worker.api.model.CreateAndStartPhaseJobRequest startRequest);
    
    public Future<com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse> createAndStartToolJob(com.ximalaya.galaxy.business.worker.api.model.CreateAndStartToolJobRequest startRequest);
  
}