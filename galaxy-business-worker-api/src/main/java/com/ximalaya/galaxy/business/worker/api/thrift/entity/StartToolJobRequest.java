/**
 * Generated by <PERSON><PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.thrift.entity;

import java.nio.ByteBuffer;
import java.util.*;

import com.ximalaya.mainstay.common.MainstayCodecException;
import com.ximalaya.mainstay.common.Option;
import com.ximalaya.mainstay.common.RouteArg;
import com.ximalaya.mainstay.common.util.Utilities;
import com.ximalaya.mainstay.rpc.thrift.ThriftStruct;
import com.ximalaya.mainstay.rpc.thrift.ThriftStructCodec;
import com.ximalaya.mainstay.rpc.thrift.TException;
import com.ximalaya.mainstay.rpc.thrift.protocol.*;


public class StartToolJobRequest implements ThriftStruct {
  private static final TStruct STRUCT = new TStruct("StartToolJobRequest");
  private static final TField UidField = new TField("uid", TType.I64, (short) 1);
  final long uid;
  boolean isSetUid;
  private static final TField SessionCodeField = new TField("sessionCode", TType.I64, (short) 2);
  final long sessionCode;
  boolean isSetSessionCode;
  private static final TField PhaseCodeField = new TField("phaseCode", TType.I64, (short) 3);
  final long phaseCode;
  boolean isSetPhaseCode;
  private static final TField ToolIdField = new TField("toolId", TType.I64, (short) 4);
  final long toolId;
  boolean isSetToolId;
  private static final TField ArgsField = new TField("args", TType.STRING, (short) 5);
  final String args;
  boolean isSetArgs;
  private static final TField NeedRemoveBlockCodesField = new TField("needRemoveBlockCodes", TType.LIST, (short) 6);
  final List<Long> needRemoveBlockCodes;
  boolean isSetNeedRemoveBlockCodes;

  public static class Builder {
    private long _uid = 0L;
    private boolean isSetUid = false;
    public Builder setUid(long value) {
      this._uid = value;
        this.isSetUid = true;
      return this;
    }


    public boolean isSetUid() {
      return isSetUid;
    }

    public long getUid() {
      return this._uid;
    }

    public Builder unsetUid() {
      this._uid = 0L;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyUid() {
      this._uid = 0L;
      this.isSetUid = true;
      return this;
    }

    private long _sessionCode = 0L;
    private boolean isSetSessionCode = false;
    public Builder setSessionCode(long value) {
      this._sessionCode = value;
        this.isSetSessionCode = true;
      return this;
    }


    public boolean isSetSessionCode() {
      return isSetSessionCode;
    }

    public long getSessionCode() {
      return this._sessionCode;
    }

    public Builder unsetSessionCode() {
      this._sessionCode = 0L;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptySessionCode() {
      this._sessionCode = 0L;
      this.isSetSessionCode = true;
      return this;
    }

    private long _phaseCode = 0L;
    private boolean isSetPhaseCode = false;
    public Builder setPhaseCode(long value) {
      this._phaseCode = value;
        this.isSetPhaseCode = true;
      return this;
    }


    public boolean isSetPhaseCode() {
      return isSetPhaseCode;
    }

    public long getPhaseCode() {
      return this._phaseCode;
    }

    public Builder unsetPhaseCode() {
      this._phaseCode = 0L;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyPhaseCode() {
      this._phaseCode = 0L;
      this.isSetPhaseCode = true;
      return this;
    }

    private long _toolId = 0L;
    private boolean isSetToolId = false;
    public Builder setToolId(long value) {
      this._toolId = value;
        this.isSetToolId = true;
      return this;
    }


    public boolean isSetToolId() {
      return isSetToolId;
    }

    public long getToolId() {
      return this._toolId;
    }

    public Builder unsetToolId() {
      this._toolId = 0L;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyToolId() {
      this._toolId = 0L;
      this.isSetToolId = true;
      return this;
    }

    private String _args = null;
    private boolean isSetArgs = false;
    public Builder setArgs(String value) {
      this._args = value;
      if (value != null) {
        this.isSetArgs = true;
      }
      return this;
    }


    public boolean isSetArgs() {
      return isSetArgs;
    }

    public String getArgs() {
      return this._args;
    }

    public Builder unsetArgs() {
      this._args = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyArgs() {
      this._args = null;
      this.isSetArgs = true;
      return this;
    }

    private List<Long> _needRemoveBlockCodes = Utilities.makeList();
    private boolean isSetNeedRemoveBlockCodes = false;
    public Builder setNeedRemoveBlockCodes(List<Long> value) {
      this._needRemoveBlockCodes = value;
      if (value != null) {
        this.isSetNeedRemoveBlockCodes = true;
      }
      return this;
    }


    public boolean isSetNeedRemoveBlockCodes() {
      return isSetNeedRemoveBlockCodes;
    }

    public List<Long> getNeedRemoveBlockCodes() {
      return this._needRemoveBlockCodes;
    }

    public Builder unsetNeedRemoveBlockCodes() {
      this._needRemoveBlockCodes = Utilities.makeList();
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyNeedRemoveBlockCodes() {
      this._needRemoveBlockCodes = Utilities.makeList();
      this.isSetNeedRemoveBlockCodes = true;
      return this;
    }


    public StartToolJobRequest build() {
      StartToolJobRequest model = new StartToolJobRequest(this._uid, this._sessionCode, this._phaseCode, this._toolId, this._args, this._needRemoveBlockCodes);
      model.isSetUid = isSetUid;
      model.isSetSessionCode = isSetSessionCode;
      model.isSetPhaseCode = isSetPhaseCode;
      model.isSetToolId = isSetToolId;
      model.isSetArgs = isSetArgs;
      model.isSetNeedRemoveBlockCodes = isSetNeedRemoveBlockCodes;
      return model;
    }
  }

  public Builder copy() {
    Builder builder = new Builder();
    if (isSetUid) builder.setUid(this.uid);
    if (isSetSessionCode) builder.setSessionCode(this.sessionCode);
    if (isSetPhaseCode) builder.setPhaseCode(this.phaseCode);
    if (isSetToolId) builder.setToolId(this.toolId);
    if (isSetArgs) builder.setArgs(this.args);
    if (isSetNeedRemoveBlockCodes) builder.setNeedRemoveBlockCodes(this.needRemoveBlockCodes);
    return builder;
  }

  public static final ThriftStructCodec<StartToolJobRequest> CODEC = new ThriftStructCodec<StartToolJobRequest>() {
    @Override
    public StartToolJobRequest decode(TProtocol _iprot) throws TException {
      Builder builder = new Builder();
      long uid = 0L;
      long sessionCode = 0L;
      long phaseCode = 0L;
      long toolId = 0L;
      String args = null;
      List<Long> needRemoveBlockCodes = Utilities.makeList();
      Boolean _done = false;
      _iprot.readStructBegin();
      while (!_done) {
        TField _field = _iprot.readFieldBegin();
        if (_field.type == TType.STOP) {
          _done = true;
        } else {
          switch (_field.id) {
            case 1: /* uid */
              switch (_field.type) {
                case TType.I64:
                  Long uid_item;
                  uid_item = _iprot.readI64();
                  uid = uid_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setUid(uid);
              break;
            case 2: /* sessionCode */
              switch (_field.type) {
                case TType.I64:
                  Long sessionCode_item;
                  sessionCode_item = _iprot.readI64();
                  sessionCode = sessionCode_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setSessionCode(sessionCode);
              break;
            case 3: /* phaseCode */
              switch (_field.type) {
                case TType.I64:
                  Long phaseCode_item;
                  phaseCode_item = _iprot.readI64();
                  phaseCode = phaseCode_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setPhaseCode(phaseCode);
              break;
            case 4: /* toolId */
              switch (_field.type) {
                case TType.I64:
                  Long toolId_item;
                  toolId_item = _iprot.readI64();
                  toolId = toolId_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setToolId(toolId);
              break;
            case 5: /* args */
              switch (_field.type) {
                case TType.STRING:
                  String args_item;
                  args_item = _iprot.readString();
                  args = args_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setArgs(args);
              break;
            case 6: /* needRemoveBlockCodes */
              switch (_field.type) {
                case TType.LIST:
                  List<Long> needRemoveBlockCodes_item;
                  TList _list_needRemoveBlockCodes_item = _iprot.readListBegin();
                  needRemoveBlockCodes_item = new ArrayList<Long>();
                  int _i_needRemoveBlockCodes_item = 0;
                  Long needRemoveBlockCodes_item_element;
                  while (_i_needRemoveBlockCodes_item < _list_needRemoveBlockCodes_item.size) {
                    needRemoveBlockCodes_item_element = _iprot.readI64();
                    needRemoveBlockCodes_item.add(needRemoveBlockCodes_item_element);
                    _i_needRemoveBlockCodes_item += 1;
                  }
                  _iprot.readListEnd();
                  needRemoveBlockCodes = needRemoveBlockCodes_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setNeedRemoveBlockCodes(needRemoveBlockCodes);
              break;
            default:
              TProtocolUtil.skip(_iprot, _field.type);
          }
          _iprot.readFieldEnd();
        }
      }
      _iprot.readStructEnd();
      try {
        return builder.build();
      } catch (IllegalStateException stateEx) {
        throw new TProtocolException(stateEx.getMessage());
      }
    }

    @Override
    public void encode(StartToolJobRequest struct, TProtocol oprot) throws TException {
      struct.write(oprot);
    }
  };

  public static StartToolJobRequest decode(TProtocol _iprot) throws TException {
    return CODEC.decode(_iprot);
  }

  public static void encode(StartToolJobRequest struct, TProtocol oprot) throws TException {
    CODEC.encode(struct, oprot);
  }

  StartToolJobRequest(long uid, long sessionCode, long phaseCode, long toolId, String args, List<Long> needRemoveBlockCodes) {
    this.uid = uid;
    this.sessionCode = sessionCode;
    this.phaseCode = phaseCode;
    this.toolId = toolId;
    this.args = args;
    this.needRemoveBlockCodes = needRemoveBlockCodes;
  }


  public long getUid() {
    return this.uid;
  }
  
  public boolean isSetUid() {
    return this.isSetUid;
  }
  public long getSessionCode() {
    return this.sessionCode;
  }
  
  public boolean isSetSessionCode() {
    return this.isSetSessionCode;
  }
  public long getPhaseCode() {
    return this.phaseCode;
  }
  
  public boolean isSetPhaseCode() {
    return this.isSetPhaseCode;
  }
  public long getToolId() {
    return this.toolId;
  }
  
  public boolean isSetToolId() {
    return this.isSetToolId;
  }
  public String getArgs() {
    return this.args;
  }
  
  public boolean isSetArgs() {
    return this.isSetArgs;
  }
  public List<Long> getNeedRemoveBlockCodes() {
    return this.needRemoveBlockCodes;
  }
  
  public boolean isSetNeedRemoveBlockCodes() {
    return this.isSetNeedRemoveBlockCodes;
  }

  @Override
  public void write(TProtocol _oprot) throws TException {
    validate();
    _oprot.writeStructBegin(STRUCT);
    if (this.isSetUid) {
        _oprot.writeFieldBegin(UidField);
        Long uid_item = uid;
        _oprot.writeI64(uid_item);
        _oprot.writeFieldEnd();
    }
    if (this.isSetSessionCode) {
        _oprot.writeFieldBegin(SessionCodeField);
        Long sessionCode_item = sessionCode;
        _oprot.writeI64(sessionCode_item);
        _oprot.writeFieldEnd();
    }
    if (this.isSetPhaseCode) {
        _oprot.writeFieldBegin(PhaseCodeField);
        Long phaseCode_item = phaseCode;
        _oprot.writeI64(phaseCode_item);
        _oprot.writeFieldEnd();
    }
    if (this.isSetToolId) {
        _oprot.writeFieldBegin(ToolIdField);
        Long toolId_item = toolId;
        _oprot.writeI64(toolId_item);
        _oprot.writeFieldEnd();
    }
    if (this.isSetArgs) {
      if (args != null) {
        _oprot.writeFieldBegin(ArgsField);
        String args_item = args;
        _oprot.writeString(args_item);
        _oprot.writeFieldEnd();
      }
    }
    if (this.isSetNeedRemoveBlockCodes) {
      if (needRemoveBlockCodes != null) {
        _oprot.writeFieldBegin(NeedRemoveBlockCodesField);
        List<Long> needRemoveBlockCodes_item = needRemoveBlockCodes;
        _oprot.writeListBegin(new TList(TType.I64, needRemoveBlockCodes_item.size()));
        for (Long needRemoveBlockCodes_item_element : needRemoveBlockCodes_item) {
          if (needRemoveBlockCodes_item_element == null) {
            throw new MainstayCodecException("list element can not be 'null', needRemoveBlockCodes_item = " + needRemoveBlockCodes_item);
          }
          _oprot.writeI64(needRemoveBlockCodes_item_element);
        }
        _oprot.writeListEnd();
        _oprot.writeFieldEnd();
      }
    }
    _oprot.writeFieldStop();
    _oprot.writeStructEnd();
  }

  private void validate() throws TProtocolException {
  }


  @Override
  public boolean equals(Object other) {
    if (!(other instanceof StartToolJobRequest)) {
      return false;
    }
    StartToolJobRequest that = (StartToolJobRequest) other;
    if (this.uid != that.uid) {
      return false;
    }
    if (this.sessionCode != that.sessionCode) {
      return false;
    }
    if (this.phaseCode != that.phaseCode) {
      return false;
    }
    if (this.toolId != that.toolId) {
      return false;
    }
    if (this.args == null) {
      if (that.args != null)
        return false;
    } else if (!this.args.equals(that.args)) {
      return false;
    }
    if (this.needRemoveBlockCodes == null) {
      if (that.needRemoveBlockCodes != null)
        return false;
    } else if (!this.needRemoveBlockCodes.equals(that.needRemoveBlockCodes)) {
      return false;
    }
    return true;
  }

  private int hash(Object... values) {
    return java.util.Arrays.hashCode(values);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("StartToolJobRequest(");
    if (this.isSetUid) {
      sb.append("uid=");
      sb.append(this.uid);
      sb.append(", ");
    }
    if (this.isSetSessionCode) {
      sb.append("sessionCode=");
      sb.append(this.sessionCode);
      sb.append(", ");
    }
    if (this.isSetPhaseCode) {
      sb.append("phaseCode=");
      sb.append(this.phaseCode);
      sb.append(", ");
    }
    if (this.isSetToolId) {
      sb.append("toolId=");
      sb.append(this.toolId);
      sb.append(", ");
    }
    if (this.isSetArgs) {
      sb.append("args=");
      sb.append(this.args);
      sb.append(", ");
    }
    if (this.isSetNeedRemoveBlockCodes) {
      sb.append("needRemoveBlockCodes=");
      sb.append(this.needRemoveBlockCodes);
      sb.append(", ");
    }
    sb.append(")");
    return sb.toString();
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + hash * hash(this.uid);
    hash = 31 * hash + hash * hash(this.sessionCode);
    hash = 31 * hash + hash * hash(this.phaseCode);
    hash = 31 * hash + hash * hash(this.toolId);
    hash = 31 * hash + hash * (this.args == null ? 0 : this.args.hashCode());
    hash = 31 * hash + hash * (this.needRemoveBlockCodes == null ? 0 : this.needRemoveBlockCodes.hashCode());
    return hash;
  }
}