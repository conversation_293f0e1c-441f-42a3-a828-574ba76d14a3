/**
 * Generated by Dal<PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.converter;

import java.nio.ByteBuffer;
import java.util.*;


public class StartSessionJobRequestConverter {

  public static com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest transform(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest target = new com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest();
    if (null != source) {
      if (source.isSetUid()) {
        Long _uid_item = source.getUid();
        target.setUid(_uid_item);
      }
      if (source.isSetSessionCode()) {
        Long _sessionCode_item = source.getSessionCode();
        target.setSessionCode(_sessionCode_item);
      }
      if (source.isSetPhaseCode()) {
        Long _phaseCode_item = source.getPhaseCode();
        target.setPhaseCode(_phaseCode_item);
      }
      if (source.isSetSystemPrompt()) {
        String _systemPrompt_item = source.getSystemPrompt();
        target.setSystemPrompt(_systemPrompt_item);
      }
      if (source.isSetPrompt()) {
        String _prompt_item = source.getPrompt();
        target.setPrompt(_prompt_item);
      }
    }
    return target;
  }

  public static com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest transform(com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest.Builder target = new com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest.Builder();
    if (null != source) {
      if (source.getUid() != null) {
        Long _uid_item = source.getUid();
        target.setUid(_uid_item);
      }
      if (source.getSessionCode() != null) {
        Long _sessionCode_item = source.getSessionCode();
        target.setSessionCode(_sessionCode_item);
      }
      if (source.getPhaseCode() != null) {
        Long _phaseCode_item = source.getPhaseCode();
        target.setPhaseCode(_phaseCode_item);
      }
      if (source.getSystemPrompt() != null) {
        String _systemPrompt_item = source.getSystemPrompt();
        target.setSystemPrompt(_systemPrompt_item);
      }
      if (source.getPrompt() != null) {
        String _prompt_item = source.getPrompt();
        target.setPrompt(_prompt_item);
      }
    }
    return target.build();
  }
}