/**
 * Generated by <PERSON><PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.thrift.entity;

import java.nio.ByteBuffer;
import java.util.*;

import com.ximalaya.mainstay.common.MainstayCodecException;
import com.ximalaya.mainstay.common.Option;
import com.ximalaya.mainstay.common.RouteArg;
import com.ximalaya.mainstay.common.util.Utilities;
import com.ximalaya.mainstay.rpc.thrift.ThriftStruct;
import com.ximalaya.mainstay.rpc.thrift.ThriftStructCodec;
import com.ximalaya.mainstay.rpc.thrift.TException;
import com.ximalaya.mainstay.rpc.thrift.protocol.*;


public class CommonJobResponse implements ThriftStruct {
  private static final TStruct STRUCT = new TStruct("CommonJobResponse");
  private static final TField CodeField = new TField("code", TType.I32, (short) 1);
  /** 结果状态 200-成功 other-失败*/
  final int code;
  boolean isSetCode;
  private static final TField MessageField = new TField("message", TType.STRING, (short) 2);
  /** 结果消息 */
  final String message;
  boolean isSetMessage;
  private static final TField DataField = new TField("data", TType.STRING, (short) 3);
  final String data;
  boolean isSetData;

  public static class Builder {
    private int _code = 0;
    private boolean isSetCode = false;
    public Builder setCode(int value) {
      this._code = value;
        this.isSetCode = true;
      return this;
    }


    public boolean isSetCode() {
      return isSetCode;
    }

    public int getCode() {
      return this._code;
    }

    public Builder unsetCode() {
      this._code = 0;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyCode() {
      this._code = 0;
      this.isSetCode = true;
      return this;
    }

    private String _message = null;
    private boolean isSetMessage = false;
    public Builder setMessage(String value) {
      this._message = value;
      if (value != null) {
        this.isSetMessage = true;
      }
      return this;
    }


    public boolean isSetMessage() {
      return isSetMessage;
    }

    public String getMessage() {
      return this._message;
    }

    public Builder unsetMessage() {
      this._message = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyMessage() {
      this._message = null;
      this.isSetMessage = true;
      return this;
    }

    private String _data = null;
    private boolean isSetData = false;
    public Builder setData(String value) {
      this._data = value;
      if (value != null) {
        this.isSetData = true;
      }
      return this;
    }


    public boolean isSetData() {
      return isSetData;
    }

    public String getData() {
      return this._data;
    }

    public Builder unsetData() {
      this._data = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyData() {
      this._data = null;
      this.isSetData = true;
      return this;
    }


    public CommonJobResponse build() {
      CommonJobResponse model = new CommonJobResponse(this._code, this._message, this._data);
      model.isSetCode = isSetCode;
      model.isSetMessage = isSetMessage;
      model.isSetData = isSetData;
      return model;
    }
  }

  public Builder copy() {
    Builder builder = new Builder();
    if (isSetCode) builder.setCode(this.code);
    if (isSetMessage) builder.setMessage(this.message);
    if (isSetData) builder.setData(this.data);
    return builder;
  }

  public static final ThriftStructCodec<CommonJobResponse> CODEC = new ThriftStructCodec<CommonJobResponse>() {
    @Override
    public CommonJobResponse decode(TProtocol _iprot) throws TException {
      Builder builder = new Builder();
      int code = 0;
      String message = null;
      String data = null;
      Boolean _done = false;
      _iprot.readStructBegin();
      while (!_done) {
        TField _field = _iprot.readFieldBegin();
        if (_field.type == TType.STOP) {
          _done = true;
        } else {
          switch (_field.id) {
            case 1: /* code */
              switch (_field.type) {
                case TType.I32:
                  Integer code_item;
                  code_item = _iprot.readI32();
                  code = code_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setCode(code);
              break;
            case 2: /* message */
              switch (_field.type) {
                case TType.STRING:
                  String message_item;
                  message_item = _iprot.readString();
                  message = message_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setMessage(message);
              break;
            case 3: /* data */
              switch (_field.type) {
                case TType.STRING:
                  String data_item;
                  data_item = _iprot.readString();
                  data = data_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setData(data);
              break;
            default:
              TProtocolUtil.skip(_iprot, _field.type);
          }
          _iprot.readFieldEnd();
        }
      }
      _iprot.readStructEnd();
      try {
        return builder.build();
      } catch (IllegalStateException stateEx) {
        throw new TProtocolException(stateEx.getMessage());
      }
    }

    @Override
    public void encode(CommonJobResponse struct, TProtocol oprot) throws TException {
      struct.write(oprot);
    }
  };

  public static CommonJobResponse decode(TProtocol _iprot) throws TException {
    return CODEC.decode(_iprot);
  }

  public static void encode(CommonJobResponse struct, TProtocol oprot) throws TException {
    CODEC.encode(struct, oprot);
  }

  CommonJobResponse(int code, String message, String data) {
    this.code = code;
    this.message = message;
    this.data = data;
  }


  public int getCode() {
    return this.code;
  }
  
  public boolean isSetCode() {
    return this.isSetCode;
  }
  public String getMessage() {
    return this.message;
  }
  
  public boolean isSetMessage() {
    return this.isSetMessage;
  }
  public String getData() {
    return this.data;
  }
  
  public boolean isSetData() {
    return this.isSetData;
  }

  @Override
  public void write(TProtocol _oprot) throws TException {
    validate();
    _oprot.writeStructBegin(STRUCT);
    if (this.isSetCode) {
        _oprot.writeFieldBegin(CodeField);
        Integer code_item = code;
        _oprot.writeI32(code_item);
        _oprot.writeFieldEnd();
    }
    if (this.isSetMessage) {
      if (message != null) {
        _oprot.writeFieldBegin(MessageField);
        String message_item = message;
        _oprot.writeString(message_item);
        _oprot.writeFieldEnd();
      }
    }
    if (this.isSetData) {
      if (data != null) {
        _oprot.writeFieldBegin(DataField);
        String data_item = data;
        _oprot.writeString(data_item);
        _oprot.writeFieldEnd();
      }
    }
    _oprot.writeFieldStop();
    _oprot.writeStructEnd();
  }

  private void validate() throws TProtocolException {
  }


  @Override
  public boolean equals(Object other) {
    if (!(other instanceof CommonJobResponse)) {
      return false;
    }
    CommonJobResponse that = (CommonJobResponse) other;
    if (this.code != that.code) {
      return false;
    }
    if (this.message == null) {
      if (that.message != null)
        return false;
    } else if (!this.message.equals(that.message)) {
      return false;
    }
    if (this.data == null) {
      if (that.data != null)
        return false;
    } else if (!this.data.equals(that.data)) {
      return false;
    }
    return true;
  }

  private int hash(Object... values) {
    return java.util.Arrays.hashCode(values);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CommonJobResponse(");
    if (this.isSetCode) {
      sb.append("code=");
      sb.append(this.code);
      sb.append(", ");
    }
    if (this.isSetMessage) {
      sb.append("message=");
      sb.append(this.message);
      sb.append(", ");
    }
    if (this.isSetData) {
      sb.append("data=");
      sb.append(this.data);
      sb.append(", ");
    }
    sb.append(")");
    return sb.toString();
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + hash * hash(this.code);
    hash = 31 * hash + hash * (this.message == null ? 0 : this.message.hashCode());
    hash = 31 * hash + hash * (this.data == null ? 0 : this.data.hashCode());
    return hash;
  }
}