package com.ximalaya.galaxy.business.common.support

import javax.servlet.http.HttpServletRequest


/**
 * 用户上下文工具类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
object UserContextHelper {
    
    private const val USER_ID_KEY = "userId"
    private const val USERNAME_KEY = "username"
    private const val PERMISSION_ID_KEY = "permissionId"
    
    /**
     * 获取当前用户ID
     */
    fun getCurrentUserId(request: HttpServletRequest): Long? {
        return request.getAttribute(USER_ID_KEY) as? Long
    }
    
    /**
     * 获取当前用户名
     */
    fun getCurrentUsername(request: HttpServletRequest): String? {
        return request.getAttribute(USERNAME_KEY) as? String
    }
    
    /**
     * 获取当前用户权限ID
     */
    fun getCurrentPermissionId(request: HttpServletRequest): Int? {
        return request.getAttribute(PERMISSION_ID_KEY) as? Int
    }
    
    /**
     * 设置用户信息到请求属性
     */
    fun setUserInfo(request: HttpServletRequest, userId: Long, username: String, permissionId: Int) {
        request.setAttribute(USER_ID_KEY, userId)
        request.setAttribute(USERNAME_KEY, username)
        request.setAttribute(PERMISSION_ID_KEY, permissionId)
    }
} 