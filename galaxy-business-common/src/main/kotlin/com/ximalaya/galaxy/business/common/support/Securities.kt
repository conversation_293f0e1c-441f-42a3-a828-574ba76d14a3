package com.ximalaya.galaxy.business.common.support

import com.github.mervick.aes_everywhere.Aes256
import java.security.MessageDigest

/**
 *<AUTHOR>
 *@create 2023-08-08 10:51
 */
object Securities {

  fun sha256(pwd: String, salt: String) = sha256(pwd + salt)

  fun sha256(input: String): String {
    val digest = MessageDigest.getInstance("SHA-256")
    val bytes = digest.digest(input.toByteArray())
    return bytes.joinToString("") { "%02x".format(it) }
  }

  fun aes256Encrypt(input: String, passphrase: String): String {
    return Aes256.encrypt(input, passphrase)
  }

  fun aes256Decrypt(crypted: String, passphrase: String): String {
    return Aes256.decrypt(crypted, passphrase)
  }

  fun generateChatHash(uids: List<String>): String {
    // 对用户ID列表进行排序 将排序后的用户ID连接成一个字符串
    val input = uids.sorted()
      .joinToString("-")

    return sha256(input)
  }

}