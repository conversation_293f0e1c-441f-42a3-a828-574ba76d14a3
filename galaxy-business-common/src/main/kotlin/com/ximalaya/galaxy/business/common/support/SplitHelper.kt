package com.ximalaya.galaxy.business.common.support

import com.google.common.base.Splitter
import java.util.stream.Collectors

/**
 *<AUTHOR>
 *@create 2024-12-10 00:01
 */
object SplitHelper {

  fun <T> deserialize(target: String?, separator: String? = null, resultConvert: (String) -> T): List<T> =
    target?.let {
      Splitter.on(separator ?: ",")
        .omitEmptyStrings()
        .trimResults()
        .splitToList(it)
        .map { resultConvert(it) }
    } ?: emptyList()

  fun <T> serialize(source: Collection<T>?, separator: String? = null) =
    OptionalCollection.ofNullable(source)
      .stream()
      .map { it }
      .map { it.toString() }
      .collect(Collectors.joining(separator ?: ","))

  fun <T> serialize(source: Collection<T>?, separator: String? = null, extractId: (T) -> Long) =
    OptionalCollection.ofNullable(source)
      .stream()
      .map(extractId)
      .map { it.toString() }
      .collect(Collectors.joining(separator ?: ","))

}