package com.ximalaya.galaxy.business.common.support

/**
 *<AUTHOR>
 *@create 2025-05-15 16:24
 */
class BooleanSupport<V> {

  companion object {

    fun encodeInt(value: Int): Boolean {
      return encode(value) { it != 0 }
    }

    fun decodeInt(bool: <PERSON>olean): Int {
      return decode(bool) {
        if (bool) {
          1
        } else {
          0
        }
      }
    }

    fun <V> encode(value: V, encoder: (V) -> Boolean): Boolean {
      return encoder.invoke(value)
    }

    fun <V> encode(value: V, encoder: BooleanEncode<V>): Boolean {
      return encoder.encode(value)
    }

    fun <V> decode(value: <PERSON><PERSON><PERSON>, decoder: (Boolean) -> V): V {
      return decoder.invoke(value)
    }

    fun <V> decode(value: Boolean, decoder: BooleanDecode<V>): V {
      return decoder.decode(value)
    }

  }

}

interface BooleanEncode<V> {

  fun encode(value: V): Boolean

}

interface BooleanDecode<V> {

  fun decode(bool: Boolean): V

}