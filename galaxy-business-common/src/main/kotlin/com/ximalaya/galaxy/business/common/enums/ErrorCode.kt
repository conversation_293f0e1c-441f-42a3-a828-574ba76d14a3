package com.ximalaya.galaxy.business.common.enums

/**
 *<AUTHOR>
 *@create 2023-07-30 16:24
 */
enum class ErrorCode(
  val code: Int,
  val message: String
) {

  /**
   * 以下为用户身份相关错误
   */
  /**
   * 未登陆
   */
  NOT_LOGIN_ERROR(4001, "未获取到登录用户信息，请关闭页面重新打开！"),

  /**
   * 越权错误
   */
  ULTRA_VIRES_ERROR(4002, "对不起，您没有权限执行该操作！"),

  /**
   * 以下为通用运行时错误
   */
  /**
   * 未知异常
   */
  UNKNOWN_ERROR(5000, "未知异常"),

  /**
   * 参数错误
   */
  PARAMS_ERROR(5001, "参数错误"),

  /**
   * 服务器内部错误
   */
  INNER_ERROR(5002, "服务器内部错误"),

  /**
   * 断言错误
   */
  ASSERT_ERROR(5003, "断言错误"),

  /**
   * 线程打断异常
   */
  THREAD_INTERRUPTED(5004, "发生InterruptedException"),

  /**
   * 内容不存在
   */
  CONTENT_NOT_FOUND(5005, "内容不存在"),

  /**
   * 锁繁忙
   */
  LOCK_BUSY(5006, "锁繁忙"),

  /**
   * 限流
   */
  RATE_LIMIT(5007, "「Galaxy」太忙绿了，已限流，请稍后再试~"),

  /**
   * 内容不合法
   */
  CONTENT_ILLEGAL(5008, "内容不合法，已停止生成"),

  /**
   * 以下为业务运行时错误
   */
  /**
   * 会话错误
   */
  SESSION_ERROR(6001, "会话错误"),

  /**
   * 阶段错误
   */
  PHASE_ERROR(6002, "阶段错误"),

  /**
   * 阶段无法启动
   */
  PHASE_NOT_START(6002, "阶段无法启动"),

  /**
   * 数据块错误
   */
  BLOCK_ERROR(6003, "数据块错误"),

  /**
   * 访问dify异常
   */
  CALL_DIFY_ERROR(6004, "访问dify异常"),

  /**
   * 访问agent异常
   */
  CALL_AGENT_ERROR(6005, "访问agent异常"),

  /**
   * 专辑错误
   */
  ALBUM_ERROR(6006, "专辑错误"),

  /**
   * 声音错误
   */
  TRACK_ERROR(6007, "声音错误")
  ;

}