package com.ximalaya.galaxy.business.common.support

import org.springframework.stereotype.Component
import java.security.SecureRandom
import java.util.*

/**
 * 密码加密工具类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Component
class PasswordHelper {
    
    private val secureRandom = SecureRandom()
    
    /**
     * 加密密码
     */
    fun encode(password: String): String {
        // 生成随机盐值
        val salt = generateSalt()
        // 使用SHA-256加密密码
        val hashedPassword = Securities.sha256(password, salt)
        // 返回格式: {salt}:{hashedPassword}
        return "$salt:$hashedPassword"
    }
    
    /**
     * 验证密码
     */
    fun matches(rawPassword: String, encodedPassword: String): Boolean {
        return try {
            val parts = encodedPassword.split(":")
            if (parts.size != 2) return false
            
            val salt = parts[0]
            val storedHash = parts[1]
            val inputHash = Securities.sha256(rawPassword, salt)
            
            // 使用常量时间比较防止时序攻击
            constantTimeEquals(storedHash, inputHash)
        } catch (e: Exception) {
            false
        }
    }
    
    private fun generateSalt(): String {
        val saltBytes = ByteArray(16)
        secureRandom.nextBytes(saltBytes)
        return Base64.getEncoder().encodeToString(saltBytes)
    }
    
    private fun constantTimeEquals(a: String, b: String): Boolean {
        if (a.length != b.length) return false
        
        var result = 0
        for (i in a.indices) {
            result = result or (a[i].toInt() xor b[i].toInt())
        }
        return result == 0
    }
} 