package com.ximalaya.galaxy.business.common.support

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import mu.KotlinLogging
import org.apache.commons.collections4.MapUtils
import org.stringtemplate.v4.ST
import org.stringtemplate.v4.compiler.Compiler
import org.stringtemplate.v4.compiler.STLexer

/**
 *<AUTHOR>
 *@create 2025-06-25 17:42
 */
class PromptTemplate {
    private val template: String
    private val st: ST
    private val validateStFunctions = false

    constructor(template: String) {
        this.template = template
        try {
            this.st = ST(template, '{', '}');
        } catch (ex: Exception) {
            throw GalaxyException(ErrorCode.ASSERT_ERROR, "The template string is not valid.", ex);
        }
    }

    fun render(variables: Map<String, Any>?): String {
        if (MapUtils.isEmpty(variables)) {
            return template
        }

        for (entry in variables!!.entries) {
            this.st.add(entry.key, entry.value);
        }

        validate(variables.toMutableMap())

        return this.st.render();
    }

    private fun validate(templateVariables: MutableMap<String, Any?>?): MutableSet<String?> {
        val templateTokens: Set<String?> = getInputVariables()
        val modelKeys = templateVariables?.keys ?: setOf<String?>()
        val missingVariables: MutableSet<String?> = HashSet(templateTokens)
        missingVariables.removeAll(modelKeys)

        require(missingVariables.isEmpty()) {
            throw GalaxyException(ErrorCode.ASSERT_ERROR, VALIDATION_MESSAGE.format(missingVariables))
        }
        return missingVariables
    }

    private fun getInputVariables(): MutableSet<String?> {
        val tokens = st.impl.tokens
        val inputVariables: MutableSet<String?> = java.util.HashSet<String?>()
        var isInsideList = false

        for (i in 0 until tokens.size()) {
            val token = tokens[i]

            // Handle list variables with option (e.g., {items; separator=", "})
            if (token.type == STLexer.LDELIM && i + 1 < tokens.size() && tokens[i + 1].type == STLexer.ID) {
                if (i + 2 < tokens.size() && tokens[i + 2].type == STLexer.COLON) {
                    val text = tokens[i + 1].getText()
                    if (!Compiler.funcs.containsKey(text) || this.validateStFunctions) {
                        inputVariables.add(text)
                        isInsideList = true
                    }
                }
            } else if (token.type == STLexer.RDELIM) {
                isInsideList = false
            } else if (!isInsideList && token.type == STLexer.ID) {
                val isFunctionCall = (i + 1 < tokens.size() && tokens[i + 1].type == STLexer.LPAREN)
                val isDotProperty = (i > 0 && tokens[i - 1].type == STLexer.DOT)
                // Only add as variable if:
                // - Not a function call
                // - Not a built-in function used as property (unless validateStFunctions)
                if (!isFunctionCall && (!Compiler.funcs.containsKey(token.getText()) || this.validateStFunctions
                            || !(isDotProperty && Compiler.funcs.containsKey(token.getText())))
                ) {
                    inputVariables.add(token.getText())
                }
            }
        }
        return inputVariables
    }

    companion object {
        private val logger = KotlinLogging.logger { }

        private const val VALIDATION_MESSAGE: String =
            "Not all variables were replaced in the template. Missing variable names are: %s."
    }

}