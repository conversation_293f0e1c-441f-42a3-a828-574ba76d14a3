package com.ximalaya.galaxy.business.common.message

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import java.io.Serializable
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Galaxy消息实体类
 */
data class GalaxyMessage @JsonCreator constructor(
    @JsonProperty("id") val id: String,
    @JsonProperty("type") val type: String,
    @JsonProperty("content") val content: String,
    @JsonProperty("sender") val sender: String,
    @JsonProperty("receiver") val receiver: String? = null,
    @JsonProperty("timestamp") 
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer::class)
    val timestamp: LocalDateTime = LocalDateTime.now(),
    @JsonProperty("metadata") val metadata: Map<String, Any> = emptyMap()
) : Serializable {
    companion object {
        const val TOPIC = "galaxy-message-topic"
        const val PRODUCER_GROUP = "galaxy-business-worker-producer-group"
        const val CONSUMER_GROUP = "galaxy-business-boss-consumer-group"
    }
}

/**
 * 灵活的LocalDateTime反序列化器，支持多种时间格式
 */
class FlexibleLocalDateTimeDeserializer : JsonDeserializer<LocalDateTime>() {
    private val formatters = listOf(
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
        DateTimeFormatter.ISO_LOCAL_DATE_TIME
    )

    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): LocalDateTime {
        val dateString = p.text
        
        for (formatter in formatters) {
            try {
                return LocalDateTime.parse(dateString, formatter)
            } catch (e: Exception) {
                // 继续尝试下一个格式
            }
        }
        
        throw IllegalArgumentException("Unable to parse LocalDateTime from: $dateString")
    }
} 