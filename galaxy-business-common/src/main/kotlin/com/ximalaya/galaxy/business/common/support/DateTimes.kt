package com.ximalaya.galaxy.business.common.support


import com.ximalaya.galaxy.business.common.enums.ErrorCode
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit


/**
 *<AUTHOR>
 *@create 2023-07-30 16:39
 */
object DateTimes {

  private const val BEIJING_ZONE_OFFSET_ID = "+8"

  @JvmField
  val YEAR_MONTH_DAY_HOUR_MINUTE_SECOND_PATTERN = "yyyy-MM-dd HH:mm:ss"

  @JvmField
  val YEAR_MONTH_DAY_PATTERN = "yyyy-MM-dd"

  @JvmField
  val YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = DateTimeFormatter.ofPattern(YEAR_MONTH_DAY_HOUR_MINUTE_SECOND_PATTERN)

  @JvmField
  val YEAR_MONTH_DAY = DateTimeFormatter.ofPattern(YEAR_MONTH_DAY_PATTERN)

  @JvmField
  val COMPACT_YEAR_MONTH = DateTimeFormatter.ofPattern("yyyyMM")

  @JvmField
  val COMPACT_YEAR_MONTH_DAY = DateTimeFormatter.ofPattern("yyyyMMdd")

  /**
   * 转换至北京时间UNIX时间戳 精确到秒
   *
   * @return UNIX时间戳
   */
  @JvmStatic
  fun toBeijingUnixTimeSecond() = toBeijingUnixTimeSecond(LocalDateTime.now())

  /**
   * 转换至北京时间UNIX时间戳 精确到秒
   *
   * @param beijingLocalDateTime 时间
   * @return UNIX时间戳
   */
  @JvmStatic
  fun toBeijingUnixTimeSecond(beijingLocalDateTime: LocalDateTime): Long {
    GalaxyAsserts.assertNotNull(beijingLocalDateTime, ErrorCode.PARAMS_ERROR, "beijingLocalDateTime is null")
    return beijingLocalDateTime.toEpochSecond(ZoneOffset.of(BEIJING_ZONE_OFFSET_ID))
  }

  fun parseBeijingUnixTimeSecond(instant: Instant): LocalDateTime {
    return LocalDateTime.ofInstant(instant, ZoneOffset.of(BEIJING_ZONE_OFFSET_ID))
  }

  @JvmStatic
  fun standardFormat(dt: LocalDateTime?): String? {
    return dt?.format(YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
  }

  @JvmStatic
  fun compactFormat(date: LocalDate?): String? {
    return date?.format(COMPACT_YEAR_MONTH_DAY)
  }

  @JvmStatic
  fun parseCompactFormat(date: String): LocalDate? {
    return LocalDate.parse(date, COMPACT_YEAR_MONTH_DAY)
  }

  @JvmStatic
  fun parseStandardFormat(date: String): LocalDateTime? {
    return LocalDateTime.parse(date, YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
  }

  @JvmStatic
  fun parseStandardDateFormat(date: String): LocalDate? {
    return LocalDate.parse(date, YEAR_MONTH_DAY)
  }

  @JvmStatic
  fun rangeDate(date: LocalDate): Pair<LocalDateTime, LocalDateTime> {
    val startOfDay = date.atStartOfDay()
    val endOfDay = date.atTime(23, 59, 59)
    return Pair(startOfDay, endOfDay)
  }

  @JvmStatic
  fun lastWeekRange(date: LocalDate): Pair<LocalDateTime, LocalDateTime> {
    val startAt = date.minusDays(7).atStartOfDay()
    val endAt = date.atTime(23, 59, 59)
    return Pair(startAt, endAt)
  }

  fun getRemainingImportantDates(): List<LocalDate> {
    // 获取当前日期
    val today = LocalDate.now()
    // 获取当前年份
    val currentYear = today.year

    // 创建指定日期的列表
    val importantDates = listOf(
      LocalDate.of(currentYear, 3, 1),
      LocalDate.of(currentYear, 6, 1),
      LocalDate.of(currentYear, 9, 1),
      LocalDate.of(currentYear, 12, 1)
    )

    // 过滤掉已经过去的日期
    return importantDates.filter { it.isAfter(today) }
  }

}

fun Duration.isMoreThan(amount: Long, unit: ChronoUnit): Boolean {
  val timeDifference: Long = this.toMillis()

  // 转换单位为毫秒进行比较
  val thresholdInMillis: Long = unit.duration.toMillis() * amount
  return timeDifference > thresholdInMillis
}