package com.ximalaya.galaxy.business.common.config

import org.springframework.context.annotation.Configuration

/**
 * RocketMQ配置类
 * 这里可以添加一些通用的RocketMQ配置
 */
@Configuration
class RocketMQConfig {
    
    companion object {
        const val DEFAULT_TOPIC = "galaxy-message-topic"
        const val DEFAULT_PRODUCER_GROUP = "galaxy-business-worker-producer-group"
        const val DEFAULT_CONSUMER_GROUP = "galaxy-business-boss-consumer-group"
        
        // 延迟级别说明
        val DELAY_LEVELS = mapOf(
            1 to "1s",
            2 to "5s", 
            3 to "10s",
            4 to "30s",
            5 to "1m",
            6 to "2m",
            7 to "3m",
            8 to "4m",
            9 to "5m",
            10 to "6m",
            11 to "7m",
            12 to "8m",
            13 to "9m",
            14 to "10m",
            15 to "20m",
            16 to "30m",
            17 to "1h",
            18 to "2h"
        )
    }
} 