package com.ximalaya.galaxy.business.common.enums

import java.util.*

/**
 *<AUTHOR>
 *@create 2022-11-25 22:14
 */
object Enums {

  @JvmStatic
  fun <T : Any, A> parseNotNull(types: Array<T>, extract: (T) -> A, target: A, errorMessage: String): T {
    return types.find(getPredicate(extract, target)) ?: throw IllegalArgumentException(errorMessage)
  }

  @JvmStatic
  fun <T, A> parseNullable(types: Array<T>, extract: (T) -> A, target: A): T? {
    return types.find(getPredicate(extract, target))
  }

  @JvmStatic
  fun <T, A> getPredicate(extract: (T) -> A, target: A): (T) -> Boolean {
    return { type: T -> Objects.equals(target, extract.invoke(type)) }
  }

}