package com.ximalaya.galaxy.business.common.support

import io.jsonwebtoken.Claims
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.util.*
import javax.crypto.SecretKey

/**
 * JWT工具类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Component
class JwtHelper {
    
    @Value("\${jwt.secret:galaxy-business-secret-key}")
    private lateinit var secret: String
    
    @Value("\${jwt.expiration:86400}")
    private var expiration: Long = 86400
    
    @Value("\${jwt.refresh-expiration:604800}")
    private var refreshExpiration: Long = 604800
    
    private val key: SecretKey by lazy {
        Keys.hmacShaKeyFor(secret.toByteArray())
    }
    
    /**
     * 生成token
     */
    fun generateToken(userId: Long, username: String, permissionId: Int): String {
        val now = Date()
        val expiryDate = Date(now.time + expiration * 1000)
        
        return Jwts.builder()
            .subject(userId.toString())
            .claim("username", username)
            .claim("permissionId", permissionId)
            .issuedAt(now)
            .expiration(expiryDate)
            .signWith(key)
            .compact()
    }
    
    /**
     * 生成刷新token
     */
    fun generateRefreshToken(userId: Long): String {
        val now = Date()
        val expiryDate = Date(now.time + refreshExpiration * 1000)
        
        return Jwts.builder()
            .subject(userId.toString())
            .issuedAt(now)
            .expiration(expiryDate)
            .signWith(key)
            .compact()
    }
    
    /**
     * 从token中获取用户ID
     */
    fun getUserIdFromToken(token: String): Long? {
        return try {
            val claims = getClaimsFromToken(token)
            claims.subject.toLong()
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 从token中获取用户名
     */
    fun getUsernameFromToken(token: String): String? {
        return try {
            val claims = getClaimsFromToken(token)
            claims["username"] as? String
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 从token中获取权限ID
     */
    fun getPermissionIdFromToken(token: String): Int? {
        return try {
            val claims = getClaimsFromToken(token)
            claims["permissionId"] as? Int
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 验证token是否有效
     */
    fun validateToken(token: String): Boolean {
        return try {
            Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取token过期时间
     */
    fun getExpirationDateFromToken(token: String): Date? {
        return try {
            val claims = getClaimsFromToken(token)
            claims.expiration
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 检查token是否过期
     */
    fun isTokenExpired(token: String): Boolean {
        val expiration = getExpirationDateFromToken(token)
        return expiration?.before(Date()) ?: true
    }
    
    private fun getClaimsFromToken(token: String): Claims {
        return Jwts.parser()
            .verifyWith(key)
            .build()
            .parseSignedClaims(token)
            .payload
    }
} 