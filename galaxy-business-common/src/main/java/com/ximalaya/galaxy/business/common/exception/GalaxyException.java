package com.ximalaya.galaxy.business.common.exception;


import com.ximalaya.galaxy.business.common.enums.ErrorCode;

/**
 * <AUTHOR>
 * @create 2023-07-30 16:26
 */
public class GalaxyException extends RuntimeException {

    private final ErrorCode errorCode;

    public GalaxyException() {
        this.errorCode = ErrorCode.UNKNOWN_ERROR;
    }

    public GalaxyException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public GalaxyException(String message) {
        super(message);
        this.errorCode = ErrorCode.UNKNOWN_ERROR;
    }

    public GalaxyException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public GalaxyException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCode.UNKNOWN_ERROR;
    }

    public GalaxyException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

}
