package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.vo.AgentAlbumVo
import com.ximalaya.galaxy.business.business.vo.ResultVo
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 *<AUTHOR>
 *@create 2025-06-24 15:36
 */
@Api(tags = ["Agent API"])
@RestController
@RequestMapping("/agentapi")
class AgentApiController(
    private val bizAlbumTrack: BizAlbumTrack,
) {

    @ApiOperation(value = "查询专辑内容")
    @GetMapping("/album")
    fun getAlbum(@RequestParam sessionCode: Long): ResultVo<AgentAlbumVo> {
        val result = bizAlbumTrack.getAgentAlbum(sessionCode)
        return ResultVo.ok(result)
    }

    @ApiOperation(value = "保存(创建或更新)专辑")
    @PostMapping("/save-album")
    fun saveAlbum(
        @Valid @RequestBody vo: AgentAlbumVo,
        bindingResult: BindingResult
    ): ResultVo<Unit> {
        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                bizAlbumTrack.saveAgentAlbum(vo.sessionCode!!.toLong(), vo.toSaveAlbumVo())
                ResultVo.ok()
            }
        }
    }

}