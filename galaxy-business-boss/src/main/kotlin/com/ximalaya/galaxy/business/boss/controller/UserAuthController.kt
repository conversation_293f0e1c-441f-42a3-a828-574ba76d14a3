package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.BizUserAuth
import com.ximalaya.galaxy.business.business.vo.LoginRequestVo
import com.ximalaya.galaxy.business.business.vo.LoginResponseVo
import com.ximalaya.galaxy.business.business.vo.RegisterRequestVo
import com.ximalaya.galaxy.business.business.vo.UserInfoVo
import com.ximalaya.galaxy.business.business.vo.InvitationRelationVo
import com.ximalaya.galaxy.business.common.annotation.RequireAuth
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.UserContextHelper
import com.ximalaya.galaxy.business.business.vo.ResultVo
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import javax.validation.Valid
import javax.servlet.http.HttpServletRequest

/**
 * 用户认证Controller
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Api(tags = ["用户认证API"])
@RestController
@RequestMapping("/api/auth")
class UserAuthController(
    private val bizUserAuth: BizUserAuth
) {
    
    @ApiOperation(value = "用户注册")
    @PostMapping("/register")
    fun register(
            @Valid @RequestBody request: RegisterRequestVo,
            bindingResult: BindingResult
    ): ResultVo<Boolean> {
        if (bindingResult.hasErrors()) {
            return bindingResult.buildFailedResult()
        }
        
        return try {
            val result = bizUserAuth.register(request)
            if (result) {
                ResultVo.ok(true)
            } else {
                ResultVo.failed(ErrorCode.ASSERT_ERROR, "注册失败")
            }
        } catch (e: Exception) {
            ResultVo.failed(ErrorCode.ASSERT_ERROR, e.message ?: "注册失败")
        }
    }
    
    @ApiOperation(value = "用户登录")
    @PostMapping("/login")
    fun login(
        @Valid @RequestBody request: LoginRequestVo,
        bindingResult: BindingResult,
        httpRequest: HttpServletRequest
    ): ResultVo<LoginResponseVo> {
        if (bindingResult.hasErrors()) {
            return bindingResult.buildFailedResult()
        }
        
        val ipAddress = getClientIpAddress(httpRequest)
        val deviceInfo = httpRequest.getHeader("User-Agent")
        
        val response = bizUserAuth.login(request, ipAddress, deviceInfo)
        return if (response != null) {
            ResultVo.ok(response)
        } else {
            ResultVo.failed(ErrorCode.PARAMS_ERROR, "用户名或密码错误")
        }
    }
    
    @ApiOperation(value = "用户登出")
    @PostMapping("/logout")
    @RequireAuth
    fun logout(
        @RequestHeader("Authorization") authorization: String,
        httpRequest: HttpServletRequest
    ): ResultVo<Boolean> {
        val token = authorization.removePrefix("Bearer ")
        val result = bizUserAuth.logout(token)
        return ResultVo.ok(result)
    }
    
    @ApiOperation(value = "刷新token")
    @PostMapping("/refresh")
    fun refreshToken(@RequestParam refreshToken: String): ResultVo<LoginResponseVo> {
        val response = bizUserAuth.refreshToken(refreshToken)
        return if (response != null) {
            ResultVo.ok(response)
        } else {
            ResultVo.failed(ErrorCode.PARAMS_ERROR, "刷新token失败")
        }
    }
    
    @ApiOperation(value = "获取当前用户信息")
    @GetMapping("/user")
    @RequireAuth
    fun getCurrentUser(httpRequest: HttpServletRequest): ResultVo<UserInfoVo> {
        val userId = UserContextHelper.getCurrentUserId(httpRequest)
        if (userId == null) {
            return ResultVo.failed(ErrorCode.NOT_LOGIN_ERROR, "获取用户信息失败")
        }
        
        val userInfo = bizUserAuth.getUserInfo(userId)
        return if (userInfo != null) {
            ResultVo.ok(userInfo)
        } else {
            ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "用户不存在")
        }
    }
    
    @ApiOperation(value = "验证邀请码（管理员专用）")
    @PostMapping("/invitation/validate/{code}")
    @RequireAuth(permissionId = 2) // 需要管理员权限
    fun validateInvitationCode(@PathVariable code: String): ResultVo<Boolean> {
        val isValid = bizUserAuth.validateInvitationCode(code)
        return ResultVo.ok(isValid)
    }
    
    @ApiOperation(value = "生成邀请码")
    @PostMapping("/invitation/generate")
    @RequireAuth(permissionId = 2) // 需要管理员权限
    fun generateInvitationCode(
        @RequestParam count: Int,
        @RequestParam permissionId: Int,
        @RequestParam(required = false) description: String?,
        httpRequest: HttpServletRequest
    ): ResultVo<Boolean> {
        val creatorId = UserContextHelper.getCurrentUserId(httpRequest)
        val result = bizUserAuth.generateInvitationCode(count, permissionId, description, creatorId)
        return ResultVo.ok(result)
    }
    
    @ApiOperation(value = "生成个人邀请码")
    @PostMapping("/invitation/generate-personal")
    @RequireAuth
    fun generatePersonalInvitationCode(
        @RequestParam permissionId: Int,
        @RequestParam(required = false) description: String?,
        httpRequest: HttpServletRequest
    ): ResultVo<String> {
        val userId = UserContextHelper.getCurrentUserId(httpRequest)
                ?: return ResultVo.failed(ErrorCode.NOT_LOGIN_ERROR, "获取用户信息失败")
        val code = bizUserAuth.generatePersonalInvitationCode(userId, permissionId, description)
        return if (code != null) {
            ResultVo.ok(code)
        } else {
            ResultVo.failed(ErrorCode.ASSERT_ERROR, "生成邀请码失败")
        }
    }
    
    @ApiOperation(value = "获取邀请关系")
    @GetMapping("/invitation/relation")
    @RequireAuth
    fun getInvitationRelation(httpRequest: HttpServletRequest): ResultVo<InvitationRelationVo> {
        val userId = UserContextHelper.getCurrentUserId(httpRequest)
                ?: return ResultVo.failed(ErrorCode.NOT_LOGIN_ERROR, "获取用户信息失败")
        val relation = bizUserAuth.getUserInvitationRelation(userId)
        return if (relation != null) {
            ResultVo.ok(relation)
        } else {
            ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取邀请关系失败")
        }
    }
    
    private fun getClientIpAddress(request: HttpServletRequest): String? {
        var ip = request.getHeader("X-Forwarded-For")
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("X-Real-IP")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("Proxy-Client-IP")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("WL-Proxy-Client-IP")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("HTTP_CLIENT_IP")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.remoteAddr
        }
        return ip
    }
} 