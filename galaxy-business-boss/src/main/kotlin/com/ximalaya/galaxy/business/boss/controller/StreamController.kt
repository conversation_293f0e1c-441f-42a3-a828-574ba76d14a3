package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.BizAgent
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.BizStreamMessage
import com.ximalaya.galaxy.business.business.boss.vo.*
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.repo.service.GalaxyPromptService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.apache.commons.lang3.StringUtils
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import javax.validation.Valid

/**
 *<AUTHOR>
 *@create 2025-05-20 17:03
 */
@Api(tags = ["SSE API"])
@RestController
@RequestMapping("/api/stream")
class StreamController(
    private val bizAgent: BizAgent,
    private val bizGalaxyBoss: BizGalaxyBoss,
    private val bizStreamMessage: BizStreamMessage,
    private val promptService: GalaxyPromptService,
) {

    @ApiOperation("hello")
    @PostMapping("/hello")
    fun hello(): SseEmitter {
        val emitter = SseEmitter(600_000)
        bizAgent.hello(emitter)
        return emitter
    }

    @ApiOperation("连接 会话-阶段 用来获取最新状态")
    @PostMapping("/connect/session/{sessionCode}/phase/{phaseCode}")
    fun connectSessionPhase(@PathVariable sessionCode: Long, @PathVariable phaseCode: Long): SseEmitter {
        val emitter = SseEmitter(600_000)

        // fixme
        val uid = 0L
        if (uid == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID"))

            emitter.complete()
            return emitter
        }

        emitter.onCompletion {
            bizStreamMessage.releaseStreamChannel(sessionCode, phaseCode)
        }

        bizGalaxyBoss.connectSessionPhase(emitter, uid, sessionCode, phaseCode)
        return emitter
    }

    @ApiOperation("连接 会话-阶段 用来获取最新状态")
    @PostMapping("/connect/session/{sessionCode}")
    fun connectSessionPhase(
        @PathVariable sessionCode: Long,
        @Valid @RequestBody phaseNameVo: JustPhaseNameVo,
        bindingResult: BindingResult
    ): SseEmitter {
        val emitter = SseEmitter(600_000)

        // fixme
        val uid = 0L
        if (uid == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID"))

            emitter.complete()
            return emitter
        }

        if (bindingResult.hasErrors()) {
            emitter.send(bindingResult.buildFailedResult<Unit>())

            emitter.complete()
            return emitter
        }

        val phaseCode = bizGalaxyBoss.getPhaseCode(uid, sessionCode, phaseNameVo.phaseName!!)
        if (phaseCode == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "阶段不存在"))

            emitter.complete()
            return emitter
        }

        emitter.onCompletion {
            bizStreamMessage.releaseStreamChannel(sessionCode, phaseCode)
        }

        bizGalaxyBoss.connectSessionPhase(emitter, uid, sessionCode, phaseCode)
        return emitter
    }

    @ApiOperation("开启 会话-阶段 任务")
    @PostMapping("/start-session-job/session/{sessionCode}/phase/{phaseCode}")
    fun startSessionJob(
        @PathVariable sessionCode: Long,
        @PathVariable phaseCode: Long,
        @Valid @RequestBody promptVo: StartSessionJobVo,
        bindingResult: BindingResult
    ): SseEmitter {
        // fixme
        val uid = 0L

        val emitter = SseEmitter(600_000)

        if (uid == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID"))

            emitter.complete()
            return emitter
        }

        if (bindingResult.hasErrors()) {
            emitter.send(bindingResult.buildFailedResult<Unit>())

            emitter.complete()
            return emitter
        }

        val systemPrompt = promptService.selectByName(promptVo.systemPromptName!!)
        if (systemPrompt == null || StringUtils.isBlank(systemPrompt.content)) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.CONTENT_NOT_FOUND, "系统提示词不存在"))

            emitter.complete()
            return emitter
        }

        emitter.onCompletion {
            bizStreamMessage.releaseStreamChannel(sessionCode, phaseCode)
        }

        bizGalaxyBoss.startSessionJob(
            emitter,
            StartSessionJobDto(uid, sessionCode, phaseCode, systemPrompt.content!!, promptVo.prompt!!)
        )
        return emitter
    }

    @ApiOperation("开启 会话-阶段 工具任务")
    @PostMapping("/start-tool-job/session/{sessionCode}/phase/{phaseCode}/tool/{toolId}")
    fun startSessionToolJob(
        @PathVariable sessionCode: Long,
        @PathVariable phaseCode: Long,
        @PathVariable toolId: Long,
        @Valid @RequestBody toolVo: StartToolJobVo,
        bindingResult: BindingResult,
    ): SseEmitter {
        // fixme
        val uid = 0L

        val emitter = SseEmitter(600_000)

        if (uid == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID"))

            emitter.complete()
            return emitter
        }

        if (bindingResult.hasErrors()) {
            emitter.send(bindingResult.buildFailedResult<Unit>())

            emitter.complete()
            return emitter
        }

        bizGalaxyBoss.startToolJob(
            emitter,
            StartToolJobDto(uid, sessionCode, phaseCode, toolId, toolVo.args, toolVo.needRemoveBlockCodes)
        )
        return emitter
    }

}