package com.ximalaya.galaxy.business.boss.consumer

import com.ximalaya.galaxy.business.common.message.GalaxyMessage
import mu.KotlinLogging
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener
import org.apache.rocketmq.spring.core.RocketMQListener
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Component
@RocketMQMessageListener(
    topic = GalaxyMessage.TOPIC,
    consumerGroup = GalaxyMessage.CONSUMER_GROUP
)
class GalaxyMessageConsumer : RocketMQListener<GalaxyMessage> {
    
    private val logger = KotlinLogging.logger {}
    
    override fun onMessage(message: GalaxyMessage) {
        try {
            logger.info { "收到消息: ${message.id}, 类型: ${message.type}, 内容: ${message.content}" }
            
            // 根据消息类型进行不同的处理
            when (message.type) {
                "TEST" -> handleTestMessage(message)
                "NOTIFICATION" -> handleNotificationMessage(message)
                "BROADCAST" -> handleBroadcastMessage(message)
                else -> handleDefaultMessage(message)
            }
            
            logger.info { "消息处理完成: ${message.id}" }
            
        } catch (e: Exception) {
            logger.error(e) { "消息处理失败: ${message.id}" }
            // 这里可以添加重试逻辑或者将失败的消息存储到数据库
            throw e // 重新抛出异常，让RocketMQ进行重试
        }
    }
    
    /**
     * 处理测试消息
     */
    private fun handleTestMessage(message: GalaxyMessage) {
        logger.info { "处理测试消息: ${message.content}" }
        // 这里可以添加具体的业务逻辑
        // 比如更新数据库、发送通知等
    }
    
    /**
     * 处理通知消息
     */
    private fun handleNotificationMessage(message: GalaxyMessage) {
        logger.info { "处理通知消息: ${message.content}" }
        // 处理通知逻辑
        // 比如发送邮件、短信等
    }
    
    /**
     * 处理广播消息
     */
    private fun handleBroadcastMessage(message: GalaxyMessage) {
        logger.info { "处理广播消息: ${message.content}" }
        // 处理广播逻辑
        // 比如推送给所有在线用户
    }
    
    /**
     * 处理默认消息
     */
    private fun handleDefaultMessage(message: GalaxyMessage) {
        logger.info { "处理默认消息: ${message.content}" }
        // 默认处理逻辑
    }
} 