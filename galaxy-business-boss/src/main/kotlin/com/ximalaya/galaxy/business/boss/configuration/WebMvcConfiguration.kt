package com.ximalaya.galaxy.business.boss.configuration

import com.ximalaya.galaxy.business.boss.interceptor.AuthInterceptor
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

/**
 * Web配置类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Configuration
class WebMvcConfiguration(
    private val authInterceptor: AuthInterceptor
) : WebMvcConfigurer {
    
    override fun addInterceptors(registry: InterceptorRegistry) {
        // 注册认证拦截器
        registry.addInterceptor(authInterceptor)
            .addPathPatterns("/api/**")
            .excludePathPatterns(
                "/api/auth/login",
                "/api/auth/register",
                "/api/auth/refresh"
            )
    }
} 