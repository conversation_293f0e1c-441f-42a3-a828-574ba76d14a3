package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.DateTimes
import com.ximalaya.galaxy.business.service.configuration.props.GalaxyProperties
import com.ximalaya.galaxy.business.service.support.HttpSupport
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import okhttp3.OkHttpClient
import okhttp3.Request
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping

/**
 *<AUTHOR>
 *@create 2025-02-13 16:51
 */
@Controller
@Api(tags = ["HOME"])
class HomeController(
    @Qualifier("homePageHttpClient") private val homePageHttpClient: OkHttpClient,
    private val galaxyProperties: GalaxyProperties
) {

    @GetMapping(value = ["", "/", "/home", "/home/<USER>", "/index", "/index/**", "/insights", "/insights/**"])
    @ApiOperation(value = "首页", httpMethod = "GET")
    fun home(): ResponseEntity<String> {
        val headers = HttpHeaders()
        headers.add(HttpHeaders.CONTENT_TYPE, "text/html; charset=UTF-8")

        val httpRequest = Request.Builder()
            .url(galaxyProperties.homePagePath + DateTimes.toBeijingUnixTimeSecond())
            .get()
            .build()

        val homePage = HttpSupport.syncCall(homePageHttpClient, httpRequest, ErrorCode.INNER_ERROR)

        return ResponseEntity<String>(homePage, headers, HttpStatus.OK)
    }

}