package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.vo.*
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 *<AUTHOR>
 *@create 2025-06-02 10:37
 */
@Api(tags = ["专辑API"])
@RestController
@RequestMapping("/api/album")
class AlbumController(
    private val bizAlbumTrack: BizAlbumTrack,
) {

    @ApiOperation(value = "分页获取专辑")
    @GetMapping("/page")
    fun getAlbums(
        @RequestParam("pageNum", required = false) pageNum: Long?,
        @RequestParam("pageSize", required = false) pageSize: Long?
    ): ResultVo<GalaxyPage<AlbumVo?>> {
//        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        val pageRequest = QueryPageRequestVo()
        pageNum?.let {
            pageRequest.pageNum = it
        }
        pageSize?.let {
            pageRequest.pageSize = it
        }

        // fixme
        return ResultVo.ok(bizAlbumTrack.getAlbums(0, pageRequest))
    }


    @ApiOperation(value = "获取专辑和声音")
    @GetMapping("/session/{sessionCode}")
    fun getAlbumBySessionCode(@PathVariable sessionCode: Long): ResultVo<AlbumTrackVo> {
//        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        // fixme
        return ResultVo.ok(bizAlbumTrack.getAlbumTrack(0, sessionCode))
    }

    @ApiOperation(value = "删除专辑")
    @DeleteMapping("/session/{sessionCode}")
    fun removeAlbum(
        @PathVariable sessionCode: Long,
    ): ResultVo<Boolean> {
//        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        // fixme
        return ResultVo.ok(bizAlbumTrack.removeAlbum(0, sessionCode))
    }

    @ApiOperation(value = "保存(创建或更新)专辑")
    @PostMapping("/save")
    fun saveAlbum(
        @Valid @RequestBody vo: UserAlbumVo,
        bindingResult: BindingResult
    ): ResultVo<Unit> {
        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                val agentAlbum = vo.toAgentAlbumVo()
                bizAlbumTrack.saveAgentAlbum(vo.sessionCode!!.toLong(), agentAlbum.toSaveAlbumVo())
                ResultVo.ok()
            }
        }
    }

}