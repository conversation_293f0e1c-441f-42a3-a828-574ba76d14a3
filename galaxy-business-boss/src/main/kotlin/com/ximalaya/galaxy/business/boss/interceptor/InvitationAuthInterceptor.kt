package com.ximalaya.galaxy.business.boss.interceptor

import com.alibaba.fastjson.JSON
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.APPLICATION_JSON_UTF8
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.BooleanSupport
import com.ximalaya.galaxy.business.repo.service.InvitationCodeService
import com.ximalaya.galaxy.business.service.support.getUserInvitationCacheKey
import mu.KotlinLogging
import org.redisson.api.RedissonClient
import org.redisson.client.codec.IntegerCodec
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import java.util.concurrent.TimeUnit
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 *<AUTHOR>
 *@create 2025-05-15 16:04
 */
@Component
class InvitationAuthInterceptor(
  private val redissonClient: RedissonClient,
  private val invitationCodeService: InvitationCodeService
) : HandlerInterceptor {

  override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Boolean {
    // 检查是否已经被邀请了
    // fixme
    val uid = 0L

    val invitationBucket = redissonClient.getBucket<Int>(getUserInvitationCacheKey(uid), IntegerCodec.INSTANCE)
    val invitation = invitationBucket.get()

    // redis不存在
    if (!invitationBucket.isExists || invitation == null) {
      val entity = invitationCodeService.selectByUid(uid)

      // 被邀请了
      if (entity != null) {
        // 设置redis
        invitationBucket.set(BooleanSupport.decodeInt(true), 6, TimeUnit.HOURS)
        return true
      }

      // 没被邀请
      invitationBucket.set(BooleanSupport.decodeInt(false), 1, TimeUnit.HOURS)
      logger.error("uid: {} 未被邀请", uid)

      response.status = 400
      response.contentType = APPLICATION_JSON_UTF8
      response.writer.write(JSON.toJSONString(ResultVo.failed<Unit>(ErrorCode.ULTRA_VIRES_ERROR, "您还没有填写邀请码~")))
      return false
    }

    return if (BooleanSupport.encodeInt(invitation)) {
      // 被邀请了
      true
    } else {

      // 没被邀请
      logger.error("uid: {} 未被邀请", uid)

      response.status = 400
      response.contentType = APPLICATION_JSON_UTF8
      response.writer.write(JSON.toJSONString(ResultVo.failed<Unit>(ErrorCode.ULTRA_VIRES_ERROR, "您还没有填写邀请码~")))
      false
    }
  }

  companion object {

    val logger = KotlinLogging.logger { }

  }

}