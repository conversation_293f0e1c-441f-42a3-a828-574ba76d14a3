package com.ximalaya.galaxy.business.boss.interceptor

import com.alibaba.fastjson.JSON
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.annotation.RequireAuth
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.JwtHelper
import com.ximalaya.galaxy.business.repo.service.UserService
import com.ximalaya.galaxy.business.repo.service.UserTokenService
import mu.KotlinLogging
import org.springframework.stereotype.Component
import org.springframework.web.method.HandlerMethod
import org.springframework.web.servlet.HandlerInterceptor
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * 认证拦截器
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@Component
class AuthInterceptor(
    private val jwtHelper: Jwt<PERSON>el<PERSON>,
    private val userTokenService: UserTokenService,
    private val userService: UserService
) : HandlerInterceptor {
    
    companion object {
        private val logger = KotlinLogging.logger { }
        private const val AUTHORIZATION_HEADER = "Authorization"
        private const val BEARER_PREFIX = "Bearer "
        private const val APPLICATION_JSON_UTF8 = "application/json;charset=UTF-8"
    }
    
    override fun preHandle(
            request: HttpServletRequest,
            response: HttpServletResponse,
            handler: Any
    ): Boolean {
        
        // 如果不是方法处理器，直接放行
        if (handler !is HandlerMethod) {
            return true
        }
        
        // 获取方法上的认证注解
        val requireAuth = handler.getMethodAnnotation(RequireAuth::class.java)
        
        // 如果没有认证注解，直接放行
        if (requireAuth == null) {
            return true
        }
        
        // 如果不需要认证，直接放行
        if (!requireAuth.required) {
            return true
        }
        
        // 获取token
        val token = getTokenFromRequest(request)
        if (token.isNullOrBlank()) {
            logger.warn("请求缺少token: ${request.requestURI}")
            writeErrorResponse(response, ErrorCode.NOT_LOGIN_ERROR, "请先登录")
            return false
        }
        
        // 验证token
        if (!jwtHelper.validateToken(token)) {
            logger.warn("token无效: $token")
            writeErrorResponse(response, ErrorCode.NOT_LOGIN_ERROR, "token无效")
            return false
        }
        
        // 检查token是否过期
        if (jwtHelper.isTokenExpired(token)) {
            logger.warn("token已过期: $token")
            writeErrorResponse(response, ErrorCode.NOT_LOGIN_ERROR, "token已过期")
            return false
        }
        
        // 检查token是否在数据库中存在且有效
        val tokenEntity = userTokenService.findByToken(token)
        if (tokenEntity == null) {
            logger.warn("token在数据库中不存在: $token")
            writeErrorResponse(response, ErrorCode.NOT_LOGIN_ERROR, "token无效")
            return false
        }
        
        // 获取用户信息
        val userId = jwtHelper.getUserIdFromToken(token)
        if (userId == null) {
            logger.warn("无法从token中获取用户ID: $token")
            writeErrorResponse(response, ErrorCode.NOT_LOGIN_ERROR, "token无效")
            return false
        }
        
        val user = userService.findById(userId)
        if (user == null || user.status != 1) {
            logger.warn("用户不存在或已被禁用: $userId")
            writeErrorResponse(response, ErrorCode.NOT_LOGIN_ERROR, "用户不存在或已被禁用")
            return false
        }
        
        // 检查权限
        if (requireAuth.permissionId >= 0) {
            val userPermissionId = jwtHelper.getPermissionIdFromToken(token)
            if (userPermissionId == null || userPermissionId < requireAuth.permissionId) {
                logger.warn("用户权限不足: userId=$userId, required=${requireAuth.permissionId}, actual=$userPermissionId")
                writeErrorResponse(response, ErrorCode.ULTRA_VIRES_ERROR, "权限不足")
                return false
            }
        }
        
        // 将用户信息设置到请求属性中
        request.setAttribute("userId", userId)
        request.setAttribute("username", jwtHelper.getUsernameFromToken(token))
        request.setAttribute("permissionId", jwtHelper.getPermissionIdFromToken(token))
        
        return true
    }
    
    private fun getTokenFromRequest(request: HttpServletRequest): String? {
        val authHeader = request.getHeader(AUTHORIZATION_HEADER)
        return if (authHeader != null && authHeader.startsWith(BEARER_PREFIX)) {
            authHeader.substring(BEARER_PREFIX.length)
        } else {
            null
        }
    }
    
    private fun writeErrorResponse(response: HttpServletResponse, errorCode: ErrorCode, message: String) {
        response.status = 401
        response.contentType = APPLICATION_JSON_UTF8
        response.writer.write(JSON.toJSONString(ResultVo.failed<Unit>(errorCode, message)))
    }
} 