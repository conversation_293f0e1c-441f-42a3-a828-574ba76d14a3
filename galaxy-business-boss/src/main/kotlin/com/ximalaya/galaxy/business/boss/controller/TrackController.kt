package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.BizDify
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.vo.*
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 *<AUTHOR>
 *@create 2025-06-02 17:07
 */
@Api(tags = ["声音API"])
@RestController
@RequestMapping("/api/track")
class TrackController(
    private val bizAlbumTrack: BizAlbumTrack,
    private val bizGalaxyBoss: BizGalaxyBoss,
    private val bizDify: BizDify,
) {

    @ApiOperation(value = "分页获取声音")
    @GetMapping("/session/{sessionCode}/page")
    fun getTracks(
        @RequestParam("sessionCode") sessionCode: Long,
        @RequestParam("pageNum", required = false) pageNum: Long?,
        @RequestParam("pageSize", required = false) pageSize: Long?
    ): ResultVo<GalaxyPage<TrackVo?>> {
        // fixme
        val uid = 0L

        val pageRequest = QueryPageRequestVo()
        pageNum?.let {
            pageRequest.pageNum = it
        }
        pageSize?.let {
            pageRequest.pageSize = it
        }

        return ResultVo.ok(bizAlbumTrack.getTracks(uid, sessionCode, pageRequest))
    }

    @ApiOperation(value = "获取声音列表")
    @GetMapping("/session/{sessionCode}/list")
    fun getTracks(
        @PathVariable("sessionCode") sessionCode: Long,
    ): ResultVo<List<TrackVo?>> {
        // fixme
        val uid = 0L
        return ResultVo.ok(bizAlbumTrack.getTracks(uid, sessionCode))
    }

    @ApiOperation(value = "获取声音")
    @GetMapping("/session/{sessionCode}")
    fun getTrack(
        @PathVariable sessionCode: Long,
        @RequestParam phaseName: String,
    ): ResultVo<TrackVo> {
        // fixme
        val uid = 0L
        return ResultVo.ok(bizAlbumTrack.getTrack(uid, sessionCode, phaseName))
    }

    @ApiOperation(value = "更新声音内容")
    @PostMapping("/session/{sessionCode}/phase/{phaseCode}")
    fun updateTrack(
        @PathVariable sessionCode: Long,
        @PathVariable phaseCode: Long,
        @Valid @RequestBody vo: SaveTrackContentVo,
        bindingResult: BindingResult
    ): ResultVo<Unit> {
        // fixme
        val uid = 0L
        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                // 不允许修改声音名
                vo.trackName = null
                bizAlbumTrack.updateTrack(
                    uid,
                    sessionCode,
                    phaseCode,
                    vo,
                ) { id, session ->
                    bizGalaxyBoss.checkSessionUid(id, session)
                }
                ResultVo.ok()
            }
        }
    }

    @ApiOperation(value = "删除声音")
    @DeleteMapping("/session/{sessionCode}")
    fun removeTrack(
        @PathVariable sessionCode: Long,
        @RequestParam sourceId: String,
    ): ResultVo<Boolean> {
        // fixme
        val uid = 0L
        return ResultVo.ok(bizAlbumTrack.removeTrack(uid, sessionCode, sourceId))
    }

    @ApiOperation(value = "重命名声音")
    @PostMapping("/rename")
    fun renameTrack(
        @Valid @RequestBody vo: RenameTrackVo,
        bindingResult: BindingResult
    ): ResultVo<Boolean> {

        // fixme
        val uid = 0L
        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                return ResultVo.ok(bizAlbumTrack.renameTrack(uid, vo))
            }
        }
    }

    @ApiOperation(value = "重新生成-声音父标题")
    @PostMapping("/regenerate/parent-title")
    fun regenerateParentTitle(@RequestBody vo: DirectBlockingCallDifyVo): ResultVo<Map<String, String>> {
        // fixme
        val uid = 0L
        val result = bizDify.regenerateParentTitle(uid, vo)
        return ResultVo.ok(result)
    }

    @ApiOperation(value = "重新生成-声音名称")
    @PostMapping("/regenerate/track-name")
    fun regenerateTrackName(@RequestBody vo: DirectBlockingCallDifyVo): ResultVo<Map<String, String>> {
        // fixme
        val uid = 0L
        val result = bizDify.regenerateTrackName(uid, vo)
        return ResultVo.ok(result)
    }

}