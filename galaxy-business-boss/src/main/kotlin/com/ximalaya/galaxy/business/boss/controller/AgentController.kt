package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.BizStreamMessage
import com.ximalaya.galaxy.business.business.boss.vo.*
import com.ximalaya.galaxy.business.business.vo.QueryPageRequestVo
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.common.annotation.RequireAuth
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.UserContextHelper
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import com.ximalaya.galaxy.business.repo.service.GalaxyPromptService
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.apache.commons.lang3.StringUtils
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import java.util.concurrent.TimeUnit
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid

/**
 *<AUTHOR>
 *@create 2025-05-25 23:21
 */
@Api(tags = ["智能体API"])
@RestController
@RequestMapping("/api/agent")
class AgentController(
    private val bizGalaxyBoss: BizGalaxyBoss,
    private val bizStreamMessage: BizStreamMessage,
    private val lockHelper: RedisLockHelper,
    private val promptService: GalaxyPromptService,
) {

    @ApiOperation(value = "创建会话")
    @PostMapping("/session")
    @RequireAuth
    fun createSession(@RequestBody vo: CreateSessionVo, bindingResult: BindingResult, request: HttpServletRequest): ResultVo<String> {
        val uid = UserContextHelper.getCurrentUserId(request) ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        if (bindingResult.hasErrors()) {
            return bindingResult.buildFailedResult()
        }

        val systemPrompt = promptService.selectByName(vo.systemPromptName!!)
        if (systemPrompt == null || StringUtils.isBlank(systemPrompt.content)) {
            return ResultVo.failed(ErrorCode.SESSION_ERROR, "系统提示词不存在")
        }

        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                ResultVo.ok(
                    bizGalaxyBoss.createSessionJob(
                        CreateSessionJobDto(
                            uid,
                            ALBUM_OUTLINE,
                            systemPrompt.content!!,
                            vo.prompt!!,
                            vo.businessData
                        )
                    ).toString()
                )
            }
        }
    }

    @ApiOperation(value = "获取会话信息")
    @GetMapping("/session/{sessionCode}")
    @RequireAuth
    fun getSessionPhases(@PathVariable sessionCode: Long, request: HttpServletRequest): ResultVo<SessionPhasesVo> {
        val uid = UserContextHelper.getCurrentUserId(request) ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return ResultVo.ok(bizGalaxyBoss.getSessionPhases(uid, sessionCode))
    }

    @ApiOperation(value = "更新block内容")
    @PutMapping("/session/{sessionCode}/phase/{phaseCode}/block/{blockCode}/content")
    @RequireAuth
    fun updateBlockContent(
        @PathVariable sessionCode: Long, @PathVariable phaseCode: Long, @PathVariable blockCode: Long,
        @RequestBody content: String
    ): ResultVo<Boolean> {
        return ResultVo.ok(bizGalaxyBoss.updateBlockContent(sessionCode, phaseCode, blockCode, content))
    }

    @ApiOperation(value = "获取历史会话信息")
    @GetMapping("/history/session")
    @RequireAuth
    fun getHistory(
        @RequestParam("pageNum", required = false) pageNum: Long?,
        @RequestParam("pageSize", required = false) pageSize: Long?,
        request: HttpServletRequest
    ): ResultVo<GalaxyPage<SessionVo>> {
        val uid = UserContextHelper.getCurrentUserId(request) ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        val pageRequest = QueryPageRequestVo()
        pageNum?.let {
            pageRequest.pageNum = it
        }
        pageSize?.let {
            pageRequest.pageSize = it
        }

        return ResultVo.ok(bizGalaxyBoss.getHistoricalSessions(uid, pageRequest))
    }

    @ApiOperation(value = "删除会话信息")
    @DeleteMapping("/session/{sessionCode}")
    @RequireAuth
    fun removeSession(sessionCode: Long, request: HttpServletRequest): ResultVo<Boolean> {
        val uid = UserContextHelper.getCurrentUserId(request) ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return ResultVo.ok(bizGalaxyBoss.removeSession(uid, sessionCode))
    }

    @ApiOperation("开启 会话-阶段 任务")
    @PostMapping("/start-session-job/session/{sessionCode}")
    @RequireAuth
    fun startSessionJob(
            @PathVariable sessionCode: Long,
            @Valid @RequestBody promptVo: StartSessionJobVo,
            bindingResult: BindingResult,
            request: HttpServletRequest
    ): ResultVo<Boolean> {
        val uid = UserContextHelper.getCurrentUserId(request) ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        if (StringUtils.isBlank(promptVo.phaseName)) {
            return ResultVo.failed(ErrorCode.SESSION_ERROR, "阶段名称为空")
        }

        if (bindingResult.hasErrors()) {
            return bindingResult.buildFailedResult<Boolean>()
        }

        val systemPrompt = promptService.selectByName(promptVo.systemPromptName!!)
        if (systemPrompt == null || StringUtils.isBlank(systemPrompt.content)) {
            return ResultVo.failed(ErrorCode.SESSION_ERROR, "系统提示词不存在")
        }

        return lockHelper.lockAroundAndCheck(
            lockKey = RedisLockHelper.getPhaseConnectLockKey(sessionCode, promptVo.phaseName!!),
            leaseTime = 20,
            unit = TimeUnit.SECONDS,
            lockBusyCode = ErrorCode.LOCK_BUSY,
            lockBusyMessage = "任务已开启"
        ) {
            val startDto = StartSessionJobWithPhaseNameDto(
                uid,
                sessionCode,
                promptVo.phaseName!!,
                systemPrompt.content!!,
                promptVo.prompt!!
            )

            ResultVo.ok(bizGalaxyBoss.startSessionJob(startDto))
        }!!
    }

    @ApiOperation("开启 会话-阶段 工具任务")
    @PostMapping("/start-tool-job/session/{sessionCode}/tool/{toolId}")
    @RequireAuth
    fun startToolJob(
        @PathVariable sessionCode: Long,
        @PathVariable toolId: Long,
        @Valid @RequestBody toolVo: StartToolJobVo,
        bindingResult: BindingResult,
        request: HttpServletRequest
    ): ResultVo<Boolean> {
        val uid = UserContextHelper.getCurrentUserId(request) ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        if (StringUtils.isBlank(toolVo.phaseName)) {
            return ResultVo.failed(ErrorCode.SESSION_ERROR, "阶段名称为空")
        }

        return lockHelper.lockAroundAndCheck(
            lockKey = RedisLockHelper.getPhaseConnectLockKey(sessionCode, toolVo.phaseName!!),
            leaseTime = 20,
            unit = TimeUnit.SECONDS,
            lockBusyCode = ErrorCode.LOCK_BUSY,
            lockBusyMessage = "任务已开启"
        ) {
            val startDto = StartToolJobWithPhaseNameDto(
                uid,
                sessionCode,
                toolVo.phaseName!!,
                toolId,
                toolVo.args,
                toolVo.needRemoveBlockCodes
            )

            ResultVo.ok(bizGalaxyBoss.startToolJob(startDto))
        }!!
    }

    @PostMapping("/stop/session/{sessionCode}/phase")
    @RequireAuth
    fun stopStream(
        @PathVariable sessionCode: Long,
        @Valid @RequestBody phaseNameVo: JustPhaseNameVo,
        bindingResult: BindingResult,
        request: HttpServletRequest
    ): ResultVo<Unit> {
        val uid = UserContextHelper.getCurrentUserId(request) ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        val phaseCode = bizGalaxyBoss.getPhaseCode(uid, sessionCode, phaseNameVo.phaseName!!)
        if (phaseCode == null) {
            return ResultVo.failed(ErrorCode.PHASE_ERROR, "阶段不存在")
        }
        bizStreamMessage.stop(sessionCode, phaseCode)

        return ResultVo.ok()
    }

}