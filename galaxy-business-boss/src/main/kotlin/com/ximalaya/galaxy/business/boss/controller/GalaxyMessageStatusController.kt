package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.galaxy.business.common.message.GalaxyMessage
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.web.bind.annotation.*

@Api(tags = ["Galaxy消息状态查询接口"])
@RestController
@RequestMapping("/api/galaxy/message/status")
class GalaxyMessageStatusController {
    
    @ApiOperation("获取消费者状态")
    @GetMapping("/consumer")
    fun getConsumerStatus(): Map<String, Any> {
        return mapOf(
            "topic" to GalaxyMessage.TOPIC,
            "consumerGroup" to GalaxyMessage.CONSUMER_GROUP,
            "status" to "RUNNING",
            "timestamp" to System.currentTimeMillis()
        )
    }
    
    @ApiOperation("获取消息主题信息")
    @GetMapping("/topic")
    fun getTopicInfo(): Map<String, Any> {
        return mapOf(
            "topic" to GalaxyMessage.TOPIC,
            "producerGroup" to GalaxyMessage.PRODUCER_GROUP,
            "consumerGroup" to GalaxyMessage.CONSUMER_GROUP,
            "description" to "Galaxy业务消息主题"
        )
    }
    
    @ApiOperation("健康检查")
    @GetMapping("/health")
    fun healthCheck(): Map<String, Any> {
        return mapOf(
            "status" to "UP",
            "service" to "galaxy-business-boss",
            "consumer" to "GalaxyMessageConsumer",
            "timestamp" to System.currentTimeMillis()
        )
    }
} 