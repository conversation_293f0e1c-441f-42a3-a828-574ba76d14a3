package com.ximalaya.galaxy.business.boss.configuration;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Swagger配置
 *
 * <AUTHOR>
 * @create 2022-05-20 14:52
 */
@Profile({"dev", "test"})
@Configuration
@EnableSwagger2
public class SpringFoxConfiguration {

  @Bean
  public Docket docket() {
    return new Docket(DocumentationType.SWAGGER_2)
        .directModelSubstitute(LocalDateTime.class, String.class)
        .directModelSubstitute(LocalDate.class, String.class)
        .directModelSubstitute(LocalTime.class, String.class)
        .directModelSubstitute(ZonedDateTime.class, String.class)
        .apiInfo(apiInfo())
        .enable(true)
        .select()
        //apis： 添加swagger接口提取范围
        .apis(RequestHandlerSelectors.basePackage("com.ximalaya.galaxy.business.boss.controller"))
        .paths(PathSelectors.any())
        .build();
  }

  private ApiInfo apiInfo() {
    return new ApiInfoBuilder()
        .title("Galaxy Boss")
        .description("银河智能体")
        .contact(new Contact("喜马拉雅-AI原创内容产品事业部", "www.ximalaya.com", "<EMAIL>"))
        .version("0.0.1")
        .build();
  }

}
