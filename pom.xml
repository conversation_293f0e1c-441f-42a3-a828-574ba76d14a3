<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.18</version>
    <relativePath/>
  </parent>

  <groupId>com.ximalaya</groupId>
  <artifactId>galaxy-business</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>galaxy-business</name>
  <description>Galaxy Business</description>
  <packaging>pom</packaging>

  <modules>
    <module>galaxy-business-common</module>
    <module>galaxy-business-repo</module>
    <module>galaxy-business-service</module>
    <module>galaxy-business-business</module>
    <module>galaxy-business-boss</module>
    <module>galaxy-business-worker</module>
    <module>galaxy-business-business-boss</module>
    <module>galaxy-business-business-worker</module>
  </modules>

  <properties>
    <java.version>1.8</java.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <kotlin.version>1.8.22</kotlin.version>
    <kotlin-logging.version>2.0.11</kotlin-logging.version>
    <kotlin-coroutines.version>1.7.3</kotlin-coroutines.version>
    <groovy.version>3.0.20</groovy.version>
    <groovy-sandbox.version>4.1.1</groovy-sandbox.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <springboot.version>2.7.18</springboot.version>
    <spring-cloud.version>2021.0.8</spring-cloud.version>
    <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
    <spring-framework.version>5.3.33</spring-framework.version>
    <dubbo.version>2021.1</dubbo.version>

    <lombok.version>1.18.24</lombok.version>
    <guava.version>32.1.1-jre</guava.version>
    <commons-collections4.version>4.2</commons-collections4.version>
    <commons-pool.version>1.6</commons-pool.version>
    <commons-lang3.version>3.11</commons-lang3.version>
    <fastjson.version>1.2.83</fastjson.version>
    <swagger.version>2.7.0</swagger.version>
    <swagger-annotations.version>1.5.13</swagger-annotations.version>
    <baomidou.version>3.5.1</baomidou.version>
    <druid.version>1.2.16</druid.version>
    <mysql.version>8.0.33</mysql.version>
    <jwt.version>0.12.3</jwt.version>
    <redisson.version>3.21.1</redisson.version>
    <curator-framework.version>4.2.0</curator-framework.version>
    <aes.version>1.2.7</aes.version>
    <commons-io.version>2.18.0</commons-io.version>
    <rocketmq.version>2.2.2</rocketmq.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- JVM Languages -->
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-all</artifactId>
        <version>${groovy.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.craftercms</groupId>
        <artifactId>groovy-sandbox</artifactId>
        <version>${groovy-sandbox.version}</version>
      </dependency>
      <!-- Kotlin -->
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-reflect</artifactId>
        <version>${kotlin.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk8</artifactId>
        <version>${kotlin.version}</version>
      </dependency>
      <dependency>
        <groupId>io.github.microutils</groupId>
        <artifactId>kotlin-logging-jvm</artifactId>
        <version>${kotlin-logging.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core-jvm</artifactId>
        <version>${kotlin-coroutines.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-jdk8</artifactId>
        <version>${kotlin-coroutines.version}</version>
      </dependency>

      <!-- Modules -->
      <dependency>
        <groupId>com.ximalaya</groupId>
        <artifactId>galaxy-business-common</artifactId>
        <version>0.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ximalaya</groupId>
        <artifactId>galaxy-business-repo</artifactId>
        <version>0.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ximalaya</groupId>
        <artifactId>galaxy-business-service</artifactId>
        <version>0.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ximalaya</groupId>
        <artifactId>galaxy-business-business</artifactId>
        <version>0.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ximalaya</groupId>
        <artifactId>galaxy-business-business-worker</artifactId>
        <version>0.0.1-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ximalaya</groupId>
        <artifactId>galaxy-business-business-boss</artifactId>
        <version>0.0.1-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-framework-bom</artifactId>
        <version>${spring-framework.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <!-- nacos alibaba cloud -->
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-dependencies</artifactId>
        <version>${spring-cloud-alibaba.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${springboot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <!-- Dubbo -->


      <!-- Sharding JDBC -->
      <dependency>
        <groupId>org.apache.shardingsphere</groupId>
        <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
        <version>4.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis-typehandlers-jsr310</artifactId>
        <version>1.0.2</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-pool</groupId>
        <artifactId>commons-pool</artifactId>
        <version>${commons-pool.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-collections4</artifactId>
        <version>${commons-collections4.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>${druid.version}</version>
      </dependency>

      <!-- swagger -->
      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger-ui</artifactId>
        <version>3.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-models</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-annotations</artifactId>
        <version>1.6.2</version>
      </dependency>

      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${baomidou.version}</version>
      </dependency>

<!--      &lt;!&ndash; MyBatis-Plus Kotlin Extensions &ndash;&gt;-->
<!--      <dependency>-->
<!--        <groupId>com.baomidou</groupId>-->
<!--        <artifactId>mybatis-plus-extension-kotlin</artifactId>-->
<!--        <version>${baomidou.version}</version>-->
<!--      </dependency>-->

      <dependency>
        <groupId>com.github.mervick</groupId>
        <artifactId>aes-everywhere-java</artifactId>
        <version>${aes.version}</version>
      </dependency>
      <dependency>
        <groupId>org.antlr</groupId>
        <artifactId>ST4</artifactId>
        <version>4.3.4</version>
      </dependency>
      <!-- JWT -->
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>${jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>${jwt.version}</version>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>${jwt.version}</version>
      </dependency>

      
<!--      &lt;!&ndash; Jakarta Servlet API &ndash;&gt;-->
<!--      <dependency>-->
<!--        <groupId>jakarta.servlet</groupId>-->
<!--        <artifactId>jakarta.servlet-api</artifactId>-->
<!--        <version>6.0.0</version>-->
<!--      </dependency>-->
<!--      <dependency>-->
<!--        <groupId>com.ximalaya.danube</groupId>-->
<!--        <artifactId>danube-core</artifactId>-->
<!--        <version>${danube-core.version}</version>-->
<!--      </dependency>-->


      <!-- 内容安全管控 -->
<!--      <dependency>-->
<!--        <groupId>com.ximalaya.audit.security</groupId>-->
<!--        <artifactId>audit-security-macfarlane-api</artifactId>-->
<!--        <version>0.3.2-SNAPSHOT</version>-->
<!--      </dependency>-->

      <!-- RocketMQ -->
      <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>${rocketmq.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-maven-plugin</artifactId>
        <version>${kotlin.version}</version>
        <configuration>
          <compilerPlugins>
            <plugin>lombok</plugin>
          </compilerPlugins>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-maven-lombok</artifactId>
            <version>${kotlin.version}</version>
          </dependency>
          <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
          </dependency>
        </dependencies>
        <executions>
          <execution>
            <id>compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                <sourceDir>${project.basedir}/src/main/java</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
          <execution>
            <id>test-compile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>test-compile</goal>
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                <sourceDir>${project.basedir}/src/test/java</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.2</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
        <executions>
          <execution>
            <id>default-compile</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>default-testCompile</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>java-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
          <execution>
            <id>java-test-compile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-maven-allopen</artifactId>
            <version>${kotlin.version}</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>

</project>