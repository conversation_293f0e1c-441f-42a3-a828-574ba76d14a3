package com.ximalaya.galaxy.business.business.worker.configuration

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor


/**
 *<AUTHOR>
 *@create 2024-12-02 23:46
 */
@Configuration
class WorkerThreadPoolConfiguration {

  @Bean(name = ["workerExecutor"], destroyMethod = "shutdown")
  fun workerExecutor(): ThreadPoolTaskExecutor {
    val executor = ThreadPoolTaskExecutor()
    executor.corePoolSize = CORE_POOL_SIZE
    executor.maxPoolSize = 4 * CORE_POOL_SIZE
    executor.setQueueCapacity(QUEUE_CAPACITY)
    executor.setThreadNamePrefix("Galaxy-Worker-")
    executor.initialize()
    return executor
  }

  companion object {

    const val CORE_POOL_SIZE = 20

    const val QUEUE_CAPACITY = 0

  }


}