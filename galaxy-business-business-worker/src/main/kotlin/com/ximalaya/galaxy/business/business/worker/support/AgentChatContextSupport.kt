package com.ximalaya.galaxy.business.business.worker.support

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.enums.AgentMessageRole
import com.ximalaya.galaxy.business.service.vo.AgentContentBlockUser
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import com.ximalaya.galaxy.business.service.vo.AgentContentText
import com.ximalaya.galaxy.business.service.vo.AgentMessageVo
import java.util.*

/**
 *<AUTHOR>
 *@create 2025-06-30 10:58
 */
object AgentChatContextSupport {

    // 从后往前取5条 u3 -> a2 -> u2 -> a1 -> u1
    private val AGENT_CHAT_MAX_SIZE = 5

    private val GPT_BLOCK_TYPE =
        setOf(BlockType.TEXT, BlockType.MCP_TOOL_CALL, BlockType.MCP_TOOL_RESULT).map { it.code }
    private val E_BLOCK_TYPE = setOf(BlockType.ERROR, BlockType.EXCEPTION).map { it.code }

    private val GPT_AGENT_CONTENT_TYPE =
        setOf(
            AgentContentType.TEXT,
            AgentContentType.TEXT_DELTA,
            AgentContentType.TOOL_CALL,
            AgentContentType.TOOL_RESULT
        )
    private val E_AGENT_CONTENT_TYPE = setOf(AgentContentType.ERROR, AgentContentType.EXCEPTION)

    fun buildAgentChatContextFromCache(
        cacheMessages: List<String>
    ): List<AgentMessageVo>? {
        val allAgentMessages: Deque<MutableList<AgentContentProtocol>> = LinkedList()

        var current: MutableList<AgentContentProtocol>? = null

        for (messageJson in cacheMessages) {
            val message = AgentContentProtocol.parseJson(messageJson)

            if (AgentContentType.BLOCK_USER == message._type) {
                // 用户对话 清空current
                current = null
                allAgentMessages.addLast(mutableListOf(message))
            } else if (GPT_AGENT_CONTENT_TYPE.contains(message._type)) {
                if (current == null) {
                    current = mutableListOf(message)
                    allAgentMessages.addLast(current)
                } else {
                    current.add(message)
                }
            } else if (E_AGENT_CONTENT_TYPE.contains(message._type)) {
                throw GalaxyException(ErrorCode.CALL_AGENT_ERROR, "上下文中发现错误，无法启动会话")
            } else {
                continue
            }
        }
        // 处理成后5条
        val newAgentContent = LinkedList<AgentContentProtocol>()
        for (i in 0 until AGENT_CHAT_MAX_SIZE) {
            if (allAgentMessages.isEmpty()) {
                break
            }
            val tmp = allAgentMessages.pollLast()
            // 从尾部循环 头插
            tmp.reversed().forEach {
                newAgentContent.addFirst(it)
            }
        }

        return doBuildAgentChatContentFromCache(newAgentContent) ?: return null
    }

    private fun doBuildAgentChatContentFromCache(
        agentContents: LinkedList<AgentContentProtocol>
    ): LinkedList<AgentMessageVo>? {

        val agentMessageVos = LinkedList<AgentMessageVo>()

        for (message in agentContents) {

            when (message._type) {
                AgentContentType.PING, AgentContentType.FINISH, AgentContentType.BLOCK_SYSTEM_PROMPT, AgentContentType.BLOCK_TEXT, AgentContentType.BLOCK_TOOL_CALL, AgentContentType.BLOCK_TOOL_RESULT,
                AgentContentType.BLOCK_DIFY_CALL, AgentContentType.BLOCK_DIFY_RESULT -> {
                    continue
                }

                AgentContentType.BLOCK_USER -> {
                    agentMessageVos.add(AgentMessageVo(AgentMessageRole.USER).apply {
                        this.content = mutableListOf(
                            AgentContentText().apply {
                                this.text = (message as AgentContentBlockUser).content
                            }
                        )
                    }
                    )
                }

                AgentContentType.TEXT, AgentContentType.TEXT_DELTA -> {
                    if (agentMessageVos.isEmpty()) {
                        // 添加第一条消息
                        agentMessageVos.add(
                            AgentMessageVo(AgentMessageRole.GPT).apply {
                                this.content = mutableListOf(message)
                            }
                        )
                        continue
                    }
                    val lastMessage = agentMessageVos.last()
                    if (lastMessage._role == AgentMessageRole.GPT) {
                        // 合并
                        val lastContent = lastMessage.content?.last() as AgentContentText
                        if (lastContent._type == AgentContentType.TEXT) {
                            lastContent.text += (message as AgentContentText).text
                        } else {
                            lastMessage.content!!.add(message)
                        }
                    } else {
                        // 添加
                        agentMessageVos.add(
                            AgentMessageVo(AgentMessageRole.GPT).apply {
                                this.content = mutableListOf(message)
                            }
                        )
                    }

                }

                AgentContentType.TOOL_CALL -> {
                    if (agentMessageVos.isEmpty()) {
                        // 添加第一条消息
                        agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                            this.content = mutableListOf(message)
                        })
                        continue
                    }

                    val lastMessage = agentMessageVos.last()
                    if (lastMessage._role == AgentMessageRole.GPT) {
                        lastMessage.content!!.add(message)
                    } else {
                        // 添加
                        agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                            this.content = mutableListOf(message)
                        })
                    }

                }

                AgentContentType.TOOL_RESULT -> {
                    agentMessageVos.add(AgentMessageVo(AgentMessageRole.TOOL_RESULT).apply {
                        this.content = mutableListOf(message)
                    })
                }


                AgentContentType.ERROR, AgentContentType.EXCEPTION -> {
                    return null
                }
            }
        }
        return agentMessageVos
    }

    fun buildAgentChatContextFromDb(
        blocks: List<GalaxyBlockEntity>
    ): List<AgentMessageVo>? {
        val allAgentMessages: Deque<MutableList<GalaxyBlockEntity>> = LinkedList()
        var current: MutableList<GalaxyBlockEntity>? = null
        for (block in blocks) {
            if (BlockType.USER.equalsCode(block.blockType)) {
                // 用户对话 清空current
                current = null
                allAgentMessages.addLast(mutableListOf(block))
            } else if (GPT_BLOCK_TYPE.contains(block.blockType)) {
                if (current == null) {
                    current = mutableListOf(block)
                    allAgentMessages.addLast(current)
                } else {
                    current.add(block)
                }
            } else if (E_BLOCK_TYPE.contains(block.blockType)) {
                throw GalaxyException(ErrorCode.CALL_AGENT_ERROR, "上下文中发现错误，无法启动会话")
            } else {
                continue
            }
        }

        // 处理成后5条
        val newBlocks = LinkedList<GalaxyBlockEntity>()
        for (i in 0 until AGENT_CHAT_MAX_SIZE) {
            if (allAgentMessages.isEmpty()) {
                break
            }
            val tmp = allAgentMessages.pollLast()
            // 从尾部循环 头插
            tmp.reversed().forEach {
                newBlocks.addFirst(it)
            }
        }

        return doBuildAgentChatContextFromDb(newBlocks)
    }

    private fun doBuildAgentChatContextFromDb(
        blocks: LinkedList<GalaxyBlockEntity>
    ): LinkedList<AgentMessageVo>? {
        val agentMessageVos = LinkedList<AgentMessageVo>()

        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.USER -> {
                    agentMessageVos.add(
                        AgentMessageVo(AgentMessageRole.USER).apply {
                            this.content = mutableListOf(
                                AgentContentText().apply {
                                    this._blockCode = block.blockCode
                                    this.text = block.content
                                }
                            )
                        }
                    )
                }


                BlockType.TEXT -> {
                    if (agentMessageVos.isNotEmpty()) {
                        val lastMessage = agentMessageVos.last()
                        if (lastMessage._role == AgentMessageRole.GPT) {
                            lastMessage.content!!.add(
                                AgentContentText().apply {
                                    this._blockCode = block.blockCode
                                    this.text = block.content
                                }
                            )
                        } else {
                            agentMessageVos.add(
                                AgentMessageVo(AgentMessageRole.GPT).apply {
                                    this.content = mutableListOf(
                                        AgentContentText().apply {
                                            this._blockCode = block.blockCode
                                            this.text = block.content
                                        }
                                    )
                                }
                            )
                        }
                    } else {
                        agentMessageVos.add(
                            AgentMessageVo(AgentMessageRole.GPT).apply {
                                this.content = mutableListOf(
                                    AgentContentText().apply {
                                        this._blockCode = block.blockCode
                                        this.text = block.content
                                    }
                                )
                            }
                        )
                    }
                }

                BlockType.MCP_TOOL_CALL -> {
                    if (agentMessageVos.isEmpty()) {
                        // 添加
                        agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                            this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                        })
                        continue
                    }

                    val lastMessage = agentMessageVos.last()
                    if (lastMessage._role == AgentMessageRole.GPT) {
                        lastMessage.content!!.add(AgentContentProtocol.parseJson(block.content!!))
                    } else {
                        // 添加
                        agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                            this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                        })
                    }
                }

                BlockType.MCP_TOOL_RESULT -> {
                    agentMessageVos.add(AgentMessageVo(AgentMessageRole.TOOL_RESULT).apply {
                        this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                    })
                }

                BlockType.ERROR, BlockType.EXCEPTION -> {
                    return null
                }

                else -> continue
            }
        }

        return agentMessageVos
    }

}