package com.ximalaya.galaxy.business.business.worker.impl

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.ximalaya.galaxy.business.common.message.GalaxyMessage
import org.apache.rocketmq.spring.core.RocketMQTemplate
import org.springframework.messaging.support.MessageBuilder
import com.ximalaya.galaxy.business.business.BizAgent
import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.support.TransactionExecutor
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.business.worker.BizGalaxyWorker
import com.ximalaya.galaxy.business.business.worker.WorkerNacosConfigBean
import com.ximalaya.galaxy.business.business.worker.support.AgentChatContextSupport
import com.ximalaya.galaxy.business.business.worker.support.AgentResponseCallback
import com.ximalaya.galaxy.business.business.worker.support.ToolJobHandler
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.exception.GalaxyPhaseNotStartException
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.common.support.PromptTemplate
import com.ximalaya.galaxy.business.repo.dto.MetadataDto
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.service.*
import com.ximalaya.galaxy.business.service.ContentDetector
import com.ximalaya.galaxy.business.service.DifyService
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.enums.AgentMessageRole
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.*
import com.ximalaya.galaxy.business.worker.api.model.*
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.util.concurrent.RejectedExecutionException
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2025-05-20 16:43
 */
@Repository
class BizGalaxyWorkerImpl(
    private val transactionTemplate: TransactionTemplate,
    private val footballConfigBean: WorkerNacosConfigBean,
    @Qualifier("workerExecutor") private val workerExecutor: ThreadPoolTaskExecutor,
    private val rocketMQTemplate: RocketMQTemplate,
    private val contentDetector: ContentDetector,
    private val difyService: DifyService,
    private val stringRedisTemplate: StringRedisTemplate,
    private val redissonClient: RedissonClient,
    private val lockHelper: RedisLockHelper,
    private val bizAgent: BizAgent,
    private val bizAlbumTrack: BizAlbumTrack,
    private val sessionService: GalaxySessionService,
    private val phaseService: GalaxyPhaseService,
    private val blockService: GalaxyBlockService,
    private val toolService: GalaxyToolService,
    private val promptService: GalaxyPromptService,
) : BizGalaxyWorker {

    private fun aroundJobExecute(
        uid: Long,
        sessionCode: Long,
        phaseCode: Long,
        jobExecute: (GalaxySessionEntity, GalaxyPhaseEntity) -> Unit
    ): CommonJobResponse {
        val response = CommonJobResponse()

        val session = sessionService.selectByUidAndSessionCode(uid, sessionCode)
        GalaxyAsserts.assertNotNull(session, ErrorCode.SESSION_ERROR, "会话不存在")

        val phase = phaseService.selectBySessionCodeAndPhaseCode(sessionCode, phaseCode)
        GalaxyAsserts.assertNotNull(phase, ErrorCode.PHASE_ERROR, "阶段不存在")

        val uid = session!!.uid!!

        // 限流区分内部、外部用户
        val isInner = footballConfigBean.isInnerUser(uid)
        val maxActivePhases = if (isInner) INNER_LIMIT else OUTER_LIMIT

        // 使用Redisson的RPermitExpirableSemaphore实现限流
        val semaphoreName = RedisLockHelper.getRateLimitLockKey(uid)
        val semaphore = redissonClient.getPermitExpirableSemaphore(semaphoreName)
        // 确保信号量存在并设置最大许可数
        if (!semaphore.isExists) {
            semaphore.trySetPermits(maxActivePhases)
        }
        // 为信号量对象设置过期时间
        redissonClient.keys.expire(semaphoreName, 1, TimeUnit.HOURS)

        // 尝试获取许可，超时时间为0表示不等待
        val permitId = semaphore.tryAcquire(0, 20, TimeUnit.MINUTES)
        if (permitId == null) {
            logger.warn("用户活动阶段数超出限制, uid: {}, maxActivePhases: {}", uid, maxActivePhases)
            response.code = ErrorCode.RATE_LIMIT.code
            response.message =
                if (isInner) "您已达到内部用户限制(最多${INNER_LIMIT}个任务同时运行)" else "已限流，请稍后重试~"
            return response
        }

        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode)
        // 使用 setnx 操作替代 bucket，设置有效期为 20 分钟
        val isLockAcquired = stringRedisTemplate.opsForValue()
            .setIfAbsent(ongoingRedisKey, phaseCode.toString(), 20, TimeUnit.MINUTES)
        if (isLockAcquired != true) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", sessionCode, phaseCode)

            response.code = ErrorCode.LOCK_BUSY.code
            response.message = ErrorCode.LOCK_BUSY.message
            return response
        }

        try {
            workerExecutor.execute {
                try {
                    TransactionExecutor.create(transactionTemplate)
                        .executeRequiresNewAndThrow { _ ->
                            updatePhaseState(sessionCode, phaseCode, PhaseState.RUNNING)
                            jobExecute(session, phase!!)
                        }
                } catch (e: GalaxyPhaseNotStartException) {
                    updatePhaseState(sessionCode, phaseCode, PhaseState.FINISHED)
                } catch (e: Exception) {
                    val errorMessage = if (e is GalaxyException) {
                        sendAndSaveException(sessionCode, phaseCode, e)
                    } else {
                        sendAndSaveException(
                            sessionCode,
                            phaseCode,
                            GalaxyException(ErrorCode.CALL_DIFY_ERROR, e.message ?: e.toString())
                        )
                    }

                    saveAllBlocks(sessionCode, phaseCode, errorMessage)
                } finally {
                    // 任务完成后释放许可
                    try {
                        semaphore.release(permitId)
                    } catch (e: Exception) {
                        logger.error("释放任务信号量许可失败", e)
                    }
                }
            }

            response.code = 200
            response.message = "成功"
        } catch (re: RejectedExecutionException) {
            // 如果执行被拒绝，释放许可和锁
            try {
                semaphore.release(permitId)
            } catch (e: Exception) {
                logger.error("释放任务信号量许可失败", e)
            }

            response.code = ErrorCode.RATE_LIMIT.code
            response.message = ErrorCode.RATE_LIMIT.message
        }

        return response
    }

    override fun createSession(uid: Long, businessDataJson: String?): Long {
        val now = LocalDateTime.now()

        val sessionEntity = GalaxySessionEntity().apply {
            this.uid = uid
            this.sessionName = DEFAULT_SESSION_NAME
            this.businessData = businessDataJson
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
        }

        GalaxyAsserts.assertTrue(sessionService.save(sessionEntity), ErrorCode.SESSION_ERROR, "创建会话失败")
        // sessionCode
        return sessionEntity.id!!
    }

    override fun createPhase(uid: Long, sessionCode: Long, phaseName: String): Long {
        val sessionEntity = sessionService.selectByUidAndSessionCode(uid, sessionCode)
        GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(uid, sessionEntity!!)

        val now = LocalDateTime.now()

        return lockHelper.lockAroundAndCheck(
            lockKey = RedisLockHelper.getCreatePhaseLockKey(sessionCode, phaseName),
            leaseTime = 10,
            unit = TimeUnit.SECONDS,
            lockBusyCode = ErrorCode.LOCK_BUSY,
            lockBusyMessage = "正在创建会话中"
        ) {
            val phaseEntity = GalaxyPhaseEntity().apply {
                this.sessionCode = sessionCode
                this.phaseName = phaseName
                this.phaseState = PhaseState.INIT.code
                this.deletedAt = LogicDeleted.SAVE.getCode()
                this.createTime = now
                this.updateTime = now
            }

            GalaxyAsserts.assertTrue(phaseService.save(phaseEntity), ErrorCode.PHASE_ERROR, "创建阶段失败")
            // phaseCode
            phaseEntity.id
        }!!
    }


    override fun runSessionJob(request: StartSessionJobRequest): CommonJobResponse {
        return aroundJobExecute(request.uid, request.sessionCode, request.phaseCode) { _, phase ->
            // 先清空Redis list存储流式结果
            stringRedisTemplate.delete(getAgentMessageCacheKey(request.sessionCode, request.phaseCode))

            // 专辑大纲流程
            if (phase.phaseName == ALBUM_OUTLINE) {
                startPhaseJob(request)
                return@aroundJobExecute
            }

            val allPhases = phaseService.selectBySessionCode(request.sessionCode)
            val albumOutlinePhase = allPhases.find { it.phaseName == ALBUM_OUTLINE }
            if (albumOutlinePhase == null) {
                sendException(
                    request.sessionCode,
                    request.phaseCode,
                    GalaxyException(ErrorCode.PHASE_ERROR, "没有找到专辑大纲流程！！！")
                )

                throw GalaxyPhaseNotStartException("没有找到专辑大纲流程！！！")
            }

            startPhaseJob(request)

            return@aroundJobExecute
        }
    }

    override fun runToolJob(request: StartToolJobRequest): CommonJobResponse {
        return aroundJobExecute(request.uid, request.sessionCode, request.phaseCode) { session, _ ->
            // 先清空Redis list存储流式结果
            stringRedisTemplate.delete(getAgentMessageCacheKey(request.sessionCode, request.phaseCode))

            // 检查可删除的块是否可以删除
            if (CollectionUtils.isNotEmpty(request.needRemoveBlockCodes)) {
                // 检查数量必须是双数
                if (request.needRemoveBlockCodes.size % 2 != 0) {
                    logger.error(
                        "需要删除的块必须成对出现, Session: {}, Phase: {}, BlockCodes: {}",
                        request.sessionCode,
                        request.phaseCode,
                        request.needRemoveBlockCodes
                    )
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        GalaxyException(ErrorCode.CALL_DIFY_ERROR, "需要删除的块必须成对出现")
                    )

                    throw GalaxyPhaseNotStartException("需要删除的块必须成对出现")
                }

                val blocks = blockService.ktQuery()
                    .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
                    .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
                    .`in`(GalaxyBlockEntity::blockCode, request.needRemoveBlockCodes)
                    .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
                    .list()

                val canRemoves = blocks.filter {
                    it.blockType == BlockType.DIFY_CALL.code || it.blockType == BlockType.DIFY_RESULT.code
                }

                val callCounts = canRemoves.count {
                    it.blockType == BlockType.DIFY_CALL.code
                }
                val resultCounts = canRemoves.count {
                    it.blockType == BlockType.DIFY_RESULT.code
                }


                if (blocks.size != request.needRemoveBlockCodes.size || callCounts != resultCounts) {
                    logger.error(
                        "需要删除的块不存在或不是成对出现的, Session: {}, Phase: {}, BlockCodes: {}",
                        request.sessionCode,
                        request.phaseCode,
                        request.needRemoveBlockCodes
                    )
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        GalaxyException(ErrorCode.CALL_DIFY_ERROR, "需要删除的块不存在或不是成对出现的")
                    )

                    throw GalaxyPhaseNotStartException("需要删除的块不存在或不是成对出现的")
                }

            }

            startToolJob(request, session.uid!!)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun createAndRunSessionJob(request: CreateSessionJobRequest): CommonJobResponse {
        val sessionCode =
            createSession(request.uid, request.businessData)
        val response = createAndRunSessionJob(
            CreateAndStartPhaseJobRequest(
                request.uid,
                sessionCode,
                request.phaseName,
                request.systemPrompt,
                request.prompt
            )
        )
        response.data = sessionCode.toString()
        return response
    }

    private fun aroundCreateJobExecute(
        uid: Long,
        sessionCode: Long,
        phaseName: String,
        jobStarter: (Long, Long, Long) -> CommonJobResponse
    ): CommonJobResponse {
        // 检查uid是否与sessionCode匹配
        val session = sessionService.selectByUidAndSessionCode(uid, sessionCode)
        GalaxyAsserts.assertNotNull(session, ErrorCode.SESSION_ERROR, "会话不存在")
        checkSessionUid(uid, session!!)

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseName(sessionCode, phaseName)
        val phaseCode = if (phase != null) {
            // 没有失败正常返回
            if (PhaseState.FAILED.equalsCode(phase.phaseState)) {
                // 1.删除旧阶段 2.重新创建
                GalaxyAsserts.assertTrue(
                    phaseService.deleteBySessionCodeAndPhaseCode(sessionCode, phase.phaseCode!!),
                    ErrorCode.PHASE_ERROR,
                    "删除阶段失败"
                )
                createPhase(uid, sessionCode, phaseName)
            } else {
                phase.phaseCode!!
            }
        } else {
            // 新阶段
            createPhase(uid, sessionCode, phaseName)
        }

        val saveTrackId: Long? = if (phaseName == ALBUM_OUTLINE) {
            null
        } else {
            // 设置声音名称
            val track = bizAlbumTrack.getTrack(uid, sessionCode, phaseName)
            if (track == null) {
                throw GalaxyException(ErrorCode.SESSION_ERROR, "您还没有保存专辑哦")
            } else {
                track.id
            }
        }

        val response = jobStarter.invoke(uid, sessionCode, phaseCode)

        if (response.code == 200) {
            saveTrackId?.let {
                bizAlbumTrack.saveTrackPhaseCode(it, phaseCode)
            }
        } else {
            // 任务失败 删除锁
            stringRedisTemplate.delete(RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode))
        }

        return response
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun createAndRunSessionJob(request: CreateAndStartPhaseJobRequest): CommonJobResponse {
        return aroundCreateJobExecute(
            request.uid,
            request.sessionCode,
            request.phaseName
        ) { uid, sessionCode, phaseCode ->
            // 注入sessionCode
            val systemPrompt = PromptTemplate(request.systemPrompt).render(mapOf("sessionCode" to sessionCode))

            runSessionJob(
                StartSessionJobRequest(
                    uid,
                    sessionCode,
                    phaseCode,
                    systemPrompt,
                    request.prompt
                )
            )
        }

    }

    @Transactional(rollbackFor = [Exception::class])
    override fun createAndRunToolJob(request: CreateAndStartToolJobRequest): CommonJobResponse {
        return aroundCreateJobExecute(
            request.uid,
            request.sessionCode,
            request.phaseName
        ) { uid, sessionCode, phaseCode ->
            runToolJob(
                StartToolJobRequest(
                    uid,
                    sessionCode,
                    phaseCode,
                    request.toolId,
                    request.args,
                    request.needRemoveBlockCodes,
                )
            )
        }
    }

    override fun saveAllBlocks(sessionCode: Long, phaseCode: Long, lastMessage: AgentContentProtocol) {
        // 更新阶段状态
        if (lastMessage._type == AgentContentType.FINISH) {
            updatePhaseState(sessionCode, phaseCode, PhaseState.FINISHED)
        } else {
            updatePhaseState(sessionCode, phaseCode, PhaseState.FAILED)
        }

        // 从Redis List获取该阶段的所有消息
        val messageRedisKey = getAgentMessageCacheKey(sessionCode, phaseCode)
        val listSize = stringRedisTemplate.opsForList().size(messageRedisKey) ?: 0

        if (listSize > 0) {
            // 获取Redis list中的所有消息
            val messageJsons = stringRedisTemplate.opsForList().range(messageRedisKey, 0, listSize - 1)

            if (messageJsons.isNullOrEmpty()) {
                return
            }

            val messageVos = messageJsons.map {
                AgentContentProtocol.parseJson(it)
            }.filter {
                // exception 不需要在此落库
                AgentContentType.EXCEPTION != it._type
            }

            val blockCodeMessagesMapping = messageVos.groupBy { it._blockCode }

            for (codeMessages in blockCodeMessagesMapping.entries) {
                if (codeMessages.key == null) {
                    // 没有blockCode的 不需要落库
                    continue
                }

                val blockCode = codeMessages.key!!
                val messages = codeMessages.value.filter {
                    setOf(
                        AgentContentType.TEXT,
                        AgentContentType.TEXT_DELTA,
                    ).contains(it._type)
                }.sortedBy { it.index }

                var content = ""
                for (message in messages) {
                    when (message._type) {
                        AgentContentType.TEXT -> {
                            content += (message as AgentContentText).text
                        }

                        AgentContentType.TEXT_DELTA -> {
                            content += (message as AgentContentTextDelta).textDelta
                        }

                        else -> {
                            /* empty line */
                        }
                    }
                }

                if (StringUtils.isNotBlank(content)) {
                    blockService.updateBlock(
                        sessionCode,
                        phaseCode,
                        blockCode,
                        content
                    )
                }

                logger.debug(
                    "持久化消息到数据库, sessionCode: {}, phaseCode: {}, blockCode: {}",
                    sessionCode, phaseCode, blockCode
                )
            }

            // 清空Redis缓存
            stringRedisTemplate.delete(messageRedisKey)
        }
    }


    override fun updatePhaseState(sessionCode: Long, phaseCode: Long, phaseState: PhaseState): Boolean {
        return phaseService.ktUpdate()
            .eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .eq(GalaxyPhaseEntity::phaseCode, phaseCode)
            .set(GalaxyPhaseEntity::phaseState, phaseState.code)
            .set(GalaxyPhaseEntity::updateTime, LocalDateTime.now())
            .update()
    }

    private fun buildGalaxyMessage(
        sessionCode: Long,
        phaseCode: Long,
        agentMessage: AgentContentProtocol
    ): GalaxyMessage {
        // 构建顺序消息的key，确保相同sessionCode+phaseCode的消息发送到同一个队列
        val messageKey = if (phaseCode > 0) {
            "${sessionCode}-${phaseCode}"
        } else {
            sessionCode.toString()
        }
        
        return GalaxyMessage(
            id = "${sessionCode}-${phaseCode}-${System.currentTimeMillis()}",
            type = "AGENT_MESSAGE",
            content = agentMessage.toJson(),
            sender = "galaxy-business-worker",
            receiver = "galaxy-business-boss",
            timestamp = java.time.LocalDateTime.now(),
            metadata = mapOf(
                "sessionCode" to sessionCode,
                "phaseCode" to phaseCode,
                "messageType" to agentMessage._type.name,
                "messageKey" to messageKey
            )
        )
    }

    private fun startToolJob(request: StartToolJobRequest, uid: Long) {
        val toolEntity = toolService.selectById(request.toolId)
        if (toolEntity == null) {
            logger.error("工具不存在, Tool: {}", request.toolId)

            sendException(
                request.sessionCode,
                request.phaseCode,
                GalaxyException(ErrorCode.CALL_DIFY_ERROR, "工具: ${request.toolId} 不存在")
            )

            throw GalaxyPhaseNotStartException("工具: ${request.toolId} 不存在")
        }

        val metadata = toolEntity.metadata?.let {
            MetadataDto.parseJson(it)
        }

        if (StringUtils.isBlank(metadata?.appSecret)) {
            sendException(
                request.sessionCode,
                request.phaseCode,
                GalaxyException(ErrorCode.CALL_DIFY_ERROR, "工具: ${request.toolId} 密钥不存在")
            )

            throw GalaxyPhaseNotStartException("工具: ${request.toolId} 密钥不存在")
        }

        val difyRequest = DifyRequestVo().apply {
            this.appSecret = metadata?.appSecret
            this.user = uid.toString()
        }

        if (StringUtils.isNotBlank(request.args)) {
            difyRequest.inputs =
                JSON.parseObject(request.args, object : TypeReference<Map<String, String?>>() {})
        }

        // 使用Redis list存储流式结果
        val messageKey = getAgentMessageCacheKey(request.sessionCode, request.phaseCode)
        // 输入空消息
        val initMessage = AgentContentPing().apply {
            this._sessionCode = request.sessionCode
            this._phaseCode = request.phaseCode
            this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
            this.ts = System.currentTimeMillis()
        }
        stringRedisTemplate.opsForList().rightPush(messageKey, initMessage.toJson())

        // 为流式结果设置过期时间
        redissonClient.keys.expire(messageKey, 1, TimeUnit.HOURS)

        val blocks: List<GalaxyBlockEntity> = blockService.ktQuery()
            .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.SYSTEM_PROMPT -> {
                    // 系统提示词不需要出现在上下文
                    continue
                }

                BlockType.USER -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }.toJson())
                }

                BlockType.TEXT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentText().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this._blockCode = block.blockCode
                        this.text = block.content
                    }.toJson())
                }

                BlockType.DIFY_CALL -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockDifyCall().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this._blockCode = block.blockCode
                        this.args = DifyRequestVo.parseJson(block.content!!)
                    }.toJson())
                }

                BlockType.DIFY_RESULT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockDifyResult().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this._blockCode = block.blockCode
                        this.result = DifyResponseVo.parseJson(block.content!!)
                    }.toJson())
                }

                BlockType.ERROR, BlockType.EXCEPTION -> {
                    val exception = GalaxyException(ErrorCode.CALL_AGENT_ERROR, "上下文中发现错误，无法启动会话")
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        exception
                    )
                    throw GalaxyPhaseNotStartException("上下文中发现错误，无法启动会话")
                }

                BlockType.MCP_TOOL_CALL, BlockType.MCP_TOOL_RESULT -> {
                    stringRedisTemplate.opsForList()
                        .rightPush(messageKey, AgentContentProtocol.parseJson(block.content!!).toJson())
                }

            }
        }

        ToolJobHandler(
            request.sessionCode,
            request.phaseCode,
            request.needRemoveBlockCodes,
            request.args,
            difyRequest,
            difyService,
            this,
            blockService,
            stringRedisTemplate,
            contentDetector,
            {
                sendAndRetry(request.sessionCode, request.phaseCode, it)

            },
            {
                sendAndSaveException(request.sessionCode, request.phaseCode, it)
            }
        ).handle()
    }

    private fun startPhaseJob(request: StartSessionJobRequest) {
        // 使用Redis list存储流式结果
        val messageKey = getAgentMessageCacheKey(request.sessionCode, request.phaseCode)

        // 输入空消息
        val initMessage = AgentContentPing().apply {
            this._sessionCode = request.sessionCode
            this._phaseCode = request.phaseCode
            this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
            this.ts = System.currentTimeMillis()
        }
        stringRedisTemplate.opsForList().rightPush(messageKey, initMessage.toJson())

        // 为流式结果设置过期时间
        redissonClient.keys.expire(messageKey, 1, TimeUnit.HOURS)

        val session = sessionService.ktQuery()
            .eq(GalaxySessionEntity::uid, request.uid)
            .eq(GalaxySessionEntity::sessionCode, request.sessionCode)
            .one()

        val phase = phaseService.ktQuery()
            .eq(GalaxyPhaseEntity::sessionCode, request.sessionCode)
            .eq(GalaxyPhaseEntity::phaseCode, request.phaseCode)
            .one()

        val needCreateSessionName = phase.phaseName == ALBUM_OUTLINE && session.sessionName == DEFAULT_SESSION_NAME

        val blocks: List<GalaxyBlockEntity> = blockService.ktQuery()
            .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.SYSTEM_PROMPT -> {
                    // 系统提示词不需要出现在上下文
                    continue
                }

                BlockType.USER -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }.toJson())
                }

                BlockType.TEXT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentText().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this._blockCode = block.blockCode
                        this.text = block.content
                    }.toJson())
                }

                BlockType.DIFY_CALL -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockDifyCall().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this._blockCode = block.blockCode
                        this.args = DifyRequestVo.parseJson(block.content!!)
                    }.toJson())
                }

                BlockType.DIFY_RESULT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockDifyResult().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this._blockCode = block.blockCode
                        this.result = DifyResponseVo.parseJson(block.content!!)
                    }.toJson())
                }

                BlockType.ERROR, BlockType.EXCEPTION -> {
                    val exception = GalaxyException(ErrorCode.CALL_AGENT_ERROR, "上下文中发现错误，无法启动会话")
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        exception
                    )
                    throw GalaxyPhaseNotStartException("上下文中发现错误，无法启动会话")
                }

                BlockType.MCP_TOOL_CALL, BlockType.MCP_TOOL_RESULT -> {
                    stringRedisTemplate.opsForList()
                        .rightPush(messageKey, AgentContentProtocol.parseJson(block.content!!).toJson())
                }

            }
        }

        // 写入prompt
        val blockCode = blockService.createBlock(request.sessionCode, request.phaseCode, BlockType.USER, request.prompt)

        // 审核prompt
        val detectDetail = contentDetector.detectDetail(request.prompt)
        val detectResponse = detectDetail.second
        val isLegal = detectResponse.result == 0
        if (isLegal) {
            blockService.updateAuditBlockResult(
                request.sessionCode,
                request.phaseCode,
                blockCode,
                AuditState.SUCCESS,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )
        } else {
            logger.error(
                "内容审核不通过, Session: {}, Phase: {}, Prompt: {}",
                request.sessionCode,
                request.phaseCode,
                request.prompt
            )
            blockService.updateAuditBlockResult(
                request.sessionCode,
                request.phaseCode,
                blockCode,
                AuditState.FAILED,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )

            sendAndSaveException(request.sessionCode, request.phaseCode, GalaxyException(ErrorCode.CONTENT_ILLEGAL))
            // 阶段设置为错误
            updatePhaseState(request.sessionCode, request.phaseCode, PhaseState.FAILED)
            return
        }

        // 添加用户消息到list
        stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
            this._sessionCode = request.sessionCode
            this._phaseCode = request.phaseCode
            this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
            this._blockCode = blockCode
            this.content = request.prompt
        }.toJson())

        val agentRequest = buildAgentChatRequest(request) {
            AgentChatRequestVo().apply {
                this.system = it.first
                this.messages = it.second
            }
        } ?: return
        logger.info("请求Agent参数: {}", JSON.toJSONString(agentRequest))
        bizAgent.streamChat(
            agentRequest,
            AgentResponseCallback(
                request.sessionCode,
                request.phaseCode,
                this,
                blockService,
                redissonClient,
                stringRedisTemplate,
                contentDetector,
                {
                    sendAndRetry(request.sessionCode, request.phaseCode, it)

                },
                {
                    sendAndSaveException(request.sessionCode, request.phaseCode, it)
                },
                {
                    if (!needCreateSessionName) {
                        return@AgentResponseCallback
                    }
                    try {
                        val prompt = promptService.selectByName("会话命名")

                        prompt?.let {
                            val createSessionNameRequest = buildAgentChatRequest(request) {

                                AgentBlockChatRequestVo().apply {
                                    this.system = prompt.content
                                    this.messages = listOf(zipAgentMessages(it.second))
                                    this.maxTokens = 1000
                                }
                            }

                            createSessionNameRequest?.let { requestVo ->
                                logger.info("请求Agent参数: {}", JSON.toJSONString(requestVo))
                                val response = bizAgent.blockChat(requestVo)
                                if (StringUtils.isNotBlank(response.text)) {
                                    logger.info("为session: {}, 命名为: {}", request.sessionCode, response.text)
                                    sessionService.ktUpdate()
                                        .eq(GalaxySessionEntity::sessionCode, request.sessionCode)
                                        .set(GalaxySessionEntity::sessionName, response.text)
                                        .set(GalaxySessionEntity::updateTime, LocalDateTime.now())
                                        .update()
                                }
                            }

                        }

                    } catch (e: Exception) {
                        // 这里异常了就等待下次再对话时命名会话
                        logger.error("为会话创建名称失败", e)
                    }
                }
            )
        )
    }

    private fun zipAgentMessages(list: List<AgentMessageVo>): AgentMessageVo {
        val text = list.map {
            it.content?.filter { it._type == AgentContentType.TEXT }
        }.filter { CollectionUtils.isNotEmpty(it) }
            .flatMap {it!!.toList() }
            .map {
                (it as AgentContentText).text
            }.joinToString("\n")

        return AgentMessageVo(AgentMessageRole.USER).apply {
            this.content = mutableListOf(
                AgentContentText().apply {
                    this.text = text
                }
            )
        }
    }

    override fun sendException(
        sessionCode: Long,
        phaseCode: Long,
        galaxyException: GalaxyException
    ): AgentContentProtocol {
        val exceptionMessage = AgentContentGalaxyException().apply {
            this._sessionCode = sessionCode
            this._phaseCode = phaseCode
            this.index = getMessageIndex(sessionCode, phaseCode) + 1
            this.errorCode = galaxyException.errorCode
            this.errorMessage = galaxyException.message
        }

        // 存储错误消息到Redis list
        val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
        stringRedisTemplate.opsForList().rightPush(messageKey, exceptionMessage.toJson())

        sendAndRetry(sessionCode, phaseCode, exceptionMessage)

        return exceptionMessage
    }

    override fun sendAndSaveException(
        sessionCode: Long,
        phaseCode: Long,
        galaxyException: GalaxyException
    ): AgentContentProtocol {
        val exceptionMessage = AgentContentGalaxyException().apply {
            this._sessionCode = sessionCode
            this._phaseCode = phaseCode
            this.index = getMessageIndex(sessionCode, phaseCode) + 1
            this.errorCode = galaxyException.errorCode
            this.errorMessage = galaxyException.message
        }

        val blockCode =
            blockService.createBlock(sessionCode, phaseCode, BlockType.EXCEPTION, exceptionMessage.toJson())
        exceptionMessage._blockCode = blockCode

        // 存储错误消息到Redis list
        val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
        stringRedisTemplate.opsForList().rightPush(messageKey, exceptionMessage.toJson())

        sendAndRetry(sessionCode, phaseCode, exceptionMessage)

        return exceptionMessage
    }

    private fun <T> buildAgentChatRequest(
        request: StartSessionJobRequest,
        buildRequest: (Pair<String, List<AgentMessageVo>>) -> T
    ): T? {
        val messageRedisKey = getAgentMessageCacheKey(request.sessionCode, request.phaseCode)
        val listSize = stringRedisTemplate.opsForList().size(messageRedisKey) ?: 0

        if (listSize > 0) {
            // 从Redis list中获取所有消息
            val cacheMessages = stringRedisTemplate.opsForList().range(messageRedisKey, 0, listSize - 1)
            if (CollectionUtils.isNotEmpty(cacheMessages)) {
                try {
                    return AgentChatContextSupport.buildAgentChatContextFromCache(cacheMessages!!)
                        ?.let {
                            buildRequest.invoke(Pair(request.systemPrompt, it))
                        }
                } catch (exception: GalaxyException) {
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        exception
                    )
                    // 阶段设置为错误
                    updatePhaseState(request.sessionCode, request.phaseCode, PhaseState.FAILED)
                    return null
                }
            }
        }

        return buildAgentChatRequestFromDb(request, buildRequest)
    }

    private fun <T> buildAgentChatRequestFromDb(
        request: StartSessionJobRequest,
        buildRequest: (Pair<String, List<AgentMessageVo>>) -> T
    ): T? {
        // 如果Redis中没有缓存或缓存处理失败，则从数据库获取
        val blocks: List<GalaxyBlockEntity> = blockService.ktQuery()
            .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        try {
            return AgentChatContextSupport.buildAgentChatContextFromDb(blocks)?.let {
                buildRequest.invoke(Pair(request.systemPrompt, it))
            }
        } catch (exception: GalaxyException) {
            sendException(
                request.sessionCode,
                request.phaseCode,
                exception
            )
            // 阶段设置为错误
            updatePhaseState(request.sessionCode, request.phaseCode, PhaseState.FAILED)
            return null
        }
    }

    private fun getMessageIndex(sessionCode: Long, phaseCode: Long): Long {
        return stringRedisTemplate.opsForList().size(getAgentMessageCacheKey(sessionCode, phaseCode)) ?: 0
    }

    private fun sendAndRetry(sessionCode: Long, phaseCode: Long, agentMessage: AgentContentProtocol) {
        val maxRetries = 3
        var retryCount = 0

        while (retryCount < maxRetries) {
            try {
                val galaxyMessage = buildGalaxyMessage(sessionCode, phaseCode, agentMessage)
                
                // 构建顺序消息的key，确保相同sessionCode+phaseCode的消息发送到同一个队列
                val messageKey = if (phaseCode > 0) {
                    "${sessionCode}-${phaseCode}"
                } else {
                    sessionCode.toString()
                }
                
                val result = rocketMQTemplate.syncSendOrderly(
                    GalaxyMessage.TOPIC,
                    MessageBuilder.withPayload(galaxyMessage).build(),
                    messageKey
                )
                if (result.sendStatus.name == "SEND_OK") {
                    return
                }

                logger.warn("[Galaxy] 消息发送失败，尝试第${retryCount + 1}次重试，sessionCode=$sessionCode, phaseCode=$phaseCode")
            } catch (e: Exception) {
                logger.error(
                    "[Galaxy] 消息发送异常，尝试第${retryCount + 1}次重试，sessionCode=$sessionCode, phaseCode=$phaseCode",
                    e
                )
            }

            retryCount++
            if (retryCount < maxRetries) {
                // 使用指数退避策略，每次重试间隔增加
                Thread.sleep((100L * (1 shl retryCount)).coerceAtMost(1000L))
            }
        }

        // 所有重试都失败了
        logger.error("[Galaxy] 消息发送最终失败，已重试${maxRetries}次，sessionCode=$sessionCode, phaseCode=$phaseCode, messageType=${agentMessage._type}")
        // todo 可以考虑将失败的消息保存到数据库或其他持久化存储中，以便后续处理
    }

    override fun checkSessionUid(uid: Long, session: GalaxySessionEntity) {
        // 管理员不判断
        if (!footballConfigBean.isAdmin(uid)) {
            GalaxyAsserts.assertEquals(session.uid, uid, ErrorCode.SESSION_ERROR, "这里不是您的会话哦~")
        }
    }

    override fun checkSessionUid(emitter: SseEmitter, uid: Long, session: GalaxySessionEntity): Boolean {
        // 管理员不判断
        if (footballConfigBean.isAdmin(uid)) {
            return true
        }

        if (session.uid != uid) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "这里不是您的会话哦~"))
            emitter.complete()
            return false
        }

        return true
    }

    companion object {

        private val logger = KotlinLogging.logger { }

        private const val INNER_LIMIT = 10

        // fixme: 外部暂时限流10
        private const val OUTER_LIMIT = 10

        const val DEFAULT_SESSION_NAME = "新会话"

    }
}