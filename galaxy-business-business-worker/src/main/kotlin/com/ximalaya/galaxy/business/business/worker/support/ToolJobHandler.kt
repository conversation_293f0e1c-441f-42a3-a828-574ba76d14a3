package com.ximalaya.galaxy.business.business.worker.support

import com.alibaba.fastjson.JSON
import com.ximalaya.galaxy.business.business.worker.BizGalaxyWorker
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.support.DateTimes
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.service.ContentDetector
import com.ximalaya.galaxy.business.service.DifyService
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.*
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import kotlinx.coroutines.*
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.data.redis.core.StringRedisTemplate

/**
 *<AUTHOR>
 *@create 2025-05-30 14:21
 */
class ToolJobHandler(
    private val sessionCode: Long,
    private val phaseCode: Long,
    private val needRemoveBlockCodes: Collection<Long>?,
    private val args: String,
    private val difyRequest: DifyRequestVo,
    private val difyService: DifyService,
    private val bizGalaxyWorker: BizGalaxyWorker,
    private val blockService: GalaxyBlockService,
    private val stringRedisTemplate: StringRedisTemplate,
    private val contentDetector: ContentDetector,
    private val sendAndRetry: (AgentContentProtocol) -> Unit,
    private val sendAndSaveException: (exception: GalaxyException) -> AgentContentProtocol,
) {

    fun handle() {
        try {
            // 启动ping消息发送协程
            val pingJob = CoroutineScope(Dispatchers.IO).launch {
                while (isActive) {
                    try {
                        val pingMessage = AgentContentPing().apply {
                            this._sessionCode = <EMAIL>
                            this._phaseCode = <EMAIL>
                            this.index = getMessageIndex() + 1
                            this.ts = DateTimes.toBeijingUnixTimeSecond()
                        }
                        sendAndRetry(pingMessage)
                        delay(30000) // 30秒延迟
                    } catch (e: Exception) {
                        logger.error("发送ping消息失败", e)
                    }
                }
            }

            val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)

            val blockCode = blockService.createBlock(
                sessionCode,
                phaseCode,
                BlockType.DIFY_CALL,
                difyRequest.toJson()
            )

            val callMessage = AgentContentBlockDifyCall().apply {
                this._sessionCode = <EMAIL>
                this._phaseCode = <EMAIL>
                this.index = getMessageIndex() + 1
                this._blockCode = blockCode
                this.args = difyRequest
            }
            sendAndRetry(callMessage)
            stringRedisTemplate.opsForList().rightPush(messageKey, callMessage.toJson())

            if (StringUtils.isBlank(args)) {
                // 直接审核通过
                blockService.updateAuditBlockResult(
                    sessionCode,
                    phaseCode,
                    blockCode,
                    AuditState.SUCCESS,
                )
            } else {
                // 审核prompt
                val detectDetail = contentDetector.detectDetail(args)
                val detectResponse = detectDetail.second
                val isLegal = detectResponse.result == 0
                if (isLegal) {
                    blockService.updateAuditBlockResult(
                        sessionCode,
                        phaseCode,
                        blockCode,
                        AuditState.SUCCESS,
                        JSON.toJSONString(detectDetail.first),
                        JSON.toJSONString(detectDetail.second)
                    )
                } else {
                    logger.error(
                        "内容审核不通过, Session: {}, Phase: {}, Args: {}",
                        sessionCode,
                        phaseCode,
                        args
                    )

                    blockService.updateAuditBlockResult(
                        sessionCode,
                        phaseCode,
                        blockCode,
                        AuditState.FAILED,
                        JSON.toJSONString(detectDetail.first),
                        JSON.toJSONString(detectDetail.second)
                    )

                    throw GalaxyException(ErrorCode.CONTENT_ILLEGAL, "内容不合法，已停止生成")
                }
            }

            val difyResponse = difyService.blockingCall(difyRequest)

            if (!difyResponse.isSuccess()) {
                throw GalaxyException(ErrorCode.CALL_DIFY_ERROR, difyResponse.data?.error ?: "dify调用失败")
            }

            val resultBlockCode =
                blockService.createBlock(sessionCode, phaseCode, BlockType.DIFY_RESULT, JSON.toJSONString(difyResponse))

            val resultMessage = AgentContentBlockDifyResult().apply {
                this._sessionCode = <EMAIL>
                this._phaseCode = <EMAIL>
                this.index = getMessageIndex() + 1
                this._blockCode = resultBlockCode
                this.result = difyResponse
            }
            sendAndRetry(resultMessage)
            stringRedisTemplate.opsForList().rightPush(messageKey, resultMessage.toJson())

            // 过审
            val auditResult = auditText(difyResponse.toJson(), setOf(resultBlockCode))

            if (!auditResult.first) {
                throw auditResult.second!!
            } else {
                if (CollectionUtils.isNotEmpty(needRemoveBlockCodes)) {
                    GalaxyAsserts.assertTrue(
                        blockService.deleteBlocks(
                            sessionCode,
                            phaseCode,
                            needRemoveBlockCodes!!
                        ), ErrorCode.BLOCK_ERROR, "删除数据块失败"
                    )
                }
            }

            val finishMessage = AgentContentFinish().apply {
                this._sessionCode = <EMAIL>
                this._phaseCode = <EMAIL>
                this.index = getMessageIndex() + 1
            }
            sendAndRetry(finishMessage)
            stringRedisTemplate.opsForList().rightPush(messageKey, finishMessage.toJson())

            // 取消ping消息发送
            pingJob.cancel()

            bizGalaxyWorker.saveAllBlocks(sessionCode, phaseCode, finishMessage)

        } catch (e: Exception) {
            logger.error("[Tool] 发生异常", e)
            val errorMessage = if (e is GalaxyException) {
                sendAndSaveException(e)
            } else {
                sendAndSaveException(GalaxyException(ErrorCode.CALL_DIFY_ERROR, e.message ?: e.toString()))
            }

            bizGalaxyWorker.saveAllBlocks(sessionCode, phaseCode, errorMessage)
        } finally {
            // 任务完成或失败，删除锁
            stringRedisTemplate.delete(RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode))
        }
    }

    private fun auditText(
        auditContent: String,
        auditBlockCodes: Set<Long>
    ): Pair<Boolean, GalaxyException?> {
        val detectDetail = contentDetector.detectDetail(auditContent)
        val detectResponse = detectDetail.second
        if (detectResponse.result == 0) {
            auditBlockCodes.forEach {
                blockService.updateAuditBlockResult(
                    sessionCode,
                    phaseCode,
                    it,
                    AuditState.SUCCESS,
                    JSON.toJSONString(detectDetail.first),
                    JSON.toJSONString(detectDetail.second)
                )
            }
            return Pair(true, null)
        }

        logger.error(
            "内容审核不通过, Session: {}, Phase: {}, Prompt: {}",
            sessionCode,
            phaseCode,
            auditContent
        )

        auditBlockCodes.forEach {
            blockService.updateAuditBlockResult(
                sessionCode,
                phaseCode,
                it,
                AuditState.FAILED,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )
        }

        return Pair(false, GalaxyException(ErrorCode.CONTENT_ILLEGAL))
    }

    private fun getMessageIndex(): Long {
        return stringRedisTemplate.opsForList().size(getAgentMessageCacheKey(sessionCode, phaseCode)) ?: 0
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }

}