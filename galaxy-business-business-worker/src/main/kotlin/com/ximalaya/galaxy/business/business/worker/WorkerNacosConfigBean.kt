package com.ximalaya.galaxy.business.business.worker

import com.alibaba.nacos.api.config.annotation.NacosValue
import com.ximalaya.galaxy.business.common.support.SplitHelper
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2025-05-06 17:24
 */
@RefreshScope
@Component
class WorkerNacosConfigBean {

  @NacosValue(value = "hello.world", autoRefreshed = true)
  var helloFootball: String? = null

  @NacosValue(value = "user.inner", autoRefreshed = true)
  var innerUsers: String? = null

  fun getInnerUsers(): List<Long> {
    return SplitHelper.deserialize(innerUsers) { java.lang.Long.valueOf(it) }
  }

  fun isInnerUser(uid: Long): Boolean {
    return getInnerUsers().contains(uid)
  }

  @NacosValue(value = "galaxy.admins", autoRefreshed = true)
  var galaxyAdmins: String? = null

  fun getAdmins(): List<Long> {
    return SplitHelper.deserialize(galaxyAdmins) { java.lang.Long.valueOf(it) }
  }

  fun isAdmin(uid: Long): Boolean {
    return getAdmins().contains(uid)
  }

}