package com.ximalaya.galaxy.business.business.worker

import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse
import com.ximalaya.galaxy.business.worker.api.model.CreateSessionJobRequest
import com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest
import com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest
import com.ximalaya.galaxy.business.worker.api.model.CreateAndStartPhaseJobRequest
import com.ximalaya.galaxy.business.worker.api.model.CreateAndStartToolJobRequest
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 *<AUTHOR>
 *@create 2025-05-20 10:54
 */
interface BizGalaxyWorker {

    fun createSession(uid: Long, businessDataJson: String?): Long

    fun createPhase(uid: Long, sessionCode: Long, phaseName: String): Long

    fun runSessionJob(request: StartSessionJobRequest): CommonJobResponse

    fun runToolJob(request: StartToolJobRequest): CommonJobResponse

    fun createAndRunSessionJob(request: CreateSessionJobRequest): CommonJobResponse

    fun createAndRunSessionJob(request: CreateAndStartPhaseJobRequest): CommonJobResponse

    fun createAndRunToolJob(request: CreateAndStartToolJobRequest): CommonJobResponse

    fun saveAllBlocks(sessionCode: Long, phaseCode: Long, lastMessage: AgentContentProtocol)

    fun updatePhaseState(sessionCode: Long, phaseCode: Long, phaseState: PhaseState): Boolean

    /**
     * 发送异常 不落库
     */
    fun sendException(sessionCode: Long, phaseCode: Long, galaxyException: GalaxyException): AgentContentProtocol

    /**
     * 发送并存储异常
     */
    fun sendAndSaveException(sessionCode: Long, phaseCode: Long, galaxyException: GalaxyException): AgentContentProtocol

    fun checkSessionUid(uid: Long, session: GalaxySessionEntity)

    fun checkSessionUid(emitter: SseEmitter, uid: Long, session: GalaxySessionEntity): Boolean

}