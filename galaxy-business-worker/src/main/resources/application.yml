server:
  port: @server.port@
  servlet:
    context-path: /${spring.application.name}
    encoding:
      enabled: true
      force: true
      charset: utf-8

spring:
  application:
    name: @app.name@
  profiles:
    active: @active.profile@
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    static-path-pattern: /resources/**
  shardingsphere:
    datasource:
      names: galaxy-ds
      galaxy-ds:
        url: @jdbc.url.0@
        username: @jdbc.username.0@
        password: @jdbc.password.0@
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
    sharding:
      tables:
        gxy_session:
          actual-data-nodes: galaxy-ds.gxy_session_$->{0..9}
          table-strategy:
            standard:
              sharding-column: uid
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxySessionShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: session_code
        gxy_phase:
          actual-data-nodes: galaxy-ds.gxy_phase_$->{0..19}
          table-strategy:
            standard:
              sharding-column: session_code
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxyPhaseShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: phase_code
        gxy_block:
          actual-data-nodes: galaxy-ds.gxy_block_$->{0..19}
          table-strategy:
            standard:
              sharding-column: session_code
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxyBlockShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: block_code
  # freemarker模板引擎
  freemarker:
    template-loader-path:
      - classpath:/templates/
    cache: false
    charset: UTF-8
    content-type: text/html
    suffix: .ftlh
    allow-request-override: true
    allow-session-override: true
    expose-request-attributes: true
    expose-spring-macro-helpers: true
  # redis
  redis:
    database: @redis.database@
    host: @redis.host@
    port: @redis.port@
    password: @redis.password@
    # 链接超时时间 单位 ms（毫秒）
    timeout: 10000ms
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: -1
        # 连接池中的最大空闲连接 默认 8
        max-idle: 8
        # 连接池中的最小空闲连接 默认 0
        min-idle: 0

mainstay:
  application:
    name: ${spring.application.name}
    level: 2
    owners:
      - name: shengzhe.zhang
        email: <EMAIL>
  registry:
    provider: @mainstay.registry.provider@
  server:
    galaxy-business-worker:
      group: @mainstay.server.group@           # required if spring 2.x, spring 2.x 由于约定了yaml上的key不支持camel体，如果用的2的话请填写该group，默认以此group优先
      port: @mainstay.server.thrift.port@                # required rpc 端口
      workerSize: @mainstay.server.workSize@           # optional rpc worker 线程池线程数
      queueSize: @mainstay.server.queueSize@            # optional rpc worker 线程池队列数
      ioSize: @mainstay.server.ioSize@                  # optional netty io 线程数
      maxConnectionSize: @mainstay.server.maxConnectionSize@
      acceptorSize: @mainstay.server.maxAcquirePendingSize@
  protocol:
    codec: thrift                # optional

eros:
  xdcs:
    healthCheckUrl: /healthcheck
    logDir: /var/log
    appEventType: other
    appName: @app.name@
    envId: 1
  log:
    path: /var/log/galaxy-business-worker
  football:
    app:
      id: 15306
      name: galaxy-business-worker
  danube:
    rocket-producers:
      - groupName: pg_galaxy_business_worker_topic_galaxy_realtime_message

# RocketMQ配置
rocketmq:
  name-server: 10.146.2.122:9876
  producer:
    group: galaxy-business-worker-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    compress-message-body-threshold: 4096
    max-message-size: 4194304

# mybatis-plus
mybatis-plus:
  globalConfig:
    dbConfig:
      idType: AUTO
      tablePrefix: gxy_
  mapper-locations: classpath:mapper/*.xml

galaxy:
  home-page-path: http://static2.test.ximalaya.com/yx/galaxy-web/last/dist/index.html?v=
  security-key: 9680e7dfdd8d933554ac3e2067bd2210c1c4cc28dd6b0fb7bf8d1e01a49dfdb4
  agent:
    path: https://sse.test.ximalaya.com/galaxy-host
  dify:
    path: http://ops.pd.ximalaya.local/dify/v1/workflows/run
    app-secret:
      regenerate-parent-title: app-ZGhXSvNNWxAI4xPc6GfssuAq
      regenerate-track-name: app-tzqITOOxm8yXw7SEyGAnbOI4