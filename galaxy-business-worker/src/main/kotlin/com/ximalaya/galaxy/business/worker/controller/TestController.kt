package com.ximalaya.galaxy.business.worker.controller

import com.ximalaya.galaxy.business.common.HELLO_WORLD
import com.ximalaya.galaxy.business.common.TEST_LOCK_KEY
import com.ximalaya.galaxy.business.business.worker.WorkerNacosConfigBean
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.business.vo.ResultVo
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.swagger.annotations.ApiParam
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RLock
import org.redisson.api.RedissonClient
import org.springframework.context.annotation.Profile
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2022-11-26 22:22
 */
@Api(tags = ["测试API"])
@Profile("dev", "test")
@RestController
@RequestMapping("/test")
class TestController(
    private val footballConfigBean: WorkerNacosConfigBean,
    private val redissonClient: RedissonClient,
) {

    @ApiOperation("hello")
    @GetMapping("/echo")
    fun echo(
        @ApiParam("hello swagger") @RequestParam(
            value = "content",
            required = false
        ) content: String?
    ): ResultVo<String> {
        val result = when {
            StringUtils.isNotBlank(content) -> content
            else -> HELLO_WORLD
        }
        return ResultVo.ok(result)
    }

    @ApiOperation("hello-football")
    @GetMapping("/football")
    fun football(): ResultVo<String> = ResultVo.ok(footballConfigBean.helloFootball)

    @ApiOperation("redisson")
    @GetMapping("/redisson")
    fun redisson(): ResultVo<String> {
        val rLock: RLock = redissonClient.getLock(TEST_LOCK_KEY)
        try {
            val result = when (rLock.tryLock(0, 1, TimeUnit.SECONDS)) {
                true -> "tryLock success"
                false -> "tryLock failed"
            }

            return ResultVo.ok(result)

        } catch (_: InterruptedException) {
            Thread.currentThread().interrupt()
            throw GalaxyException(ErrorCode.THREAD_INTERRUPTED)
        } finally {
            if (rLock.isLocked && rLock.isHeldByCurrentThread) {
                rLock.unlock()
            }
        }
    }

}