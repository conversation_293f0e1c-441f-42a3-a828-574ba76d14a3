package com.ximalaya.galaxy.business.worker.interceptor

import com.alibaba.fastjson.JSON
import mu.KotlinLogging
import org.apache.commons.lang3.StringUtils
import org.springframework.web.util.ContentCachingRequestWrapper
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import javax.servlet.Filter
import javax.servlet.FilterChain
import javax.servlet.ServletRequest
import javax.servlet.ServletResponse
import javax.servlet.http.HttpServletRequest


/**
 *<AUTHOR>
 *@create 2023-11-23 14:17
 */
class HttpRequestLogFilter : Filter {

  override fun doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain) {
    val requestWrapper = ContentCachingRequestWrapper(request as HttpServletRequest)

    try {
      chain.doFilter(requestWrapper, response)
    } finally {
      postRequestLog(requestWrapper)
    }
  }

  private fun postRequestLog(request: ContentCachingRequestWrapper) {
    if (request.requestURI.contains("healthcheck")) {
      return
    }

    val parameterMap = request.parameterMap

    val requestBody = request.contentType?.let {
      if (request.contentType.contains("application/json")) {
        StringUtils.toEncodedString(request.contentAsByteArray, StandardCharsets.UTF_8)
      } else {
        StringUtils.EMPTY
      }
    }

    val cookies = request.cookies?.joinToString(";") {
      "${it.name}=${URLDecoder.decode(it.value, "UTF-8")}"
    }

    logger.info("请求 Url: {}, Params: {}, RequestBody: {}, Cookies: {}", request.requestURI, JSON.toJSONString(parameterMap), requestBody, cookies)
  }

  companion object {

    private val logger = KotlinLogging.logger { }

  }


}