package com.ximalaya.galaxy.business.worker.service

import com.ximalaya.galaxy.business.common.message.GalaxyMessage
import mu.KotlinLogging
import org.apache.rocketmq.client.producer.SendCallback
import org.apache.rocketmq.client.producer.SendResult
import org.apache.rocketmq.spring.core.RocketMQTemplate
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.*

@Service
class GalaxyMessageProducer {
    
    private val logger = KotlinLogging.logger {}
    
    @Autowired
    private lateinit var rocketMQTemplate: RocketMQTemplate
    
    /**
     * 发送同步消息
     */
    fun sendSyncMessage(message: GalaxyMessage): Boolean {
        return try {
            val result = rocketMQTemplate.syncSend(
                GalaxyMessage.TOPIC,
                MessageBuilder.withPayload(message).build()
            )
            logger.info { "同步发送消息成功: ${message.id}, sendResult: ${result.sendStatus}" }
            result.sendStatus.name == "SEND_OK"
        } catch (e: Exception) {
            logger.error(e) { "同步发送消息失败: ${message.id}" }
            false
        }
    }
    
    /**
     * 发送异步消息
     */
    fun sendAsyncMessage(message: GalaxyMessage) {
        try {
            rocketMQTemplate.asyncSend(
                GalaxyMessage.TOPIC,
                MessageBuilder.withPayload(message).build(),
                object : SendCallback {
                    override fun onSuccess(sendResult: SendResult?) {
                        logger.info { "异步发送消息成功: ${message.id}, sendResult: ${sendResult?.sendStatus}" }
                    }
                    
                    override fun onException(throwable: Throwable?) {
                        logger.error(throwable) { "异步发送消息失败: ${message.id}" }
                    }
                }
            )
        } catch (e: Exception) {
            logger.error(e) { "异步发送消息异常: ${message.id}" }
        }
    }
    
    /**
     * 发送延迟消息
     */
    fun sendDelayMessage(message: GalaxyMessage, delayLevel: Int) {
        try {
            rocketMQTemplate.syncSend(
                GalaxyMessage.TOPIC,
                MessageBuilder.withPayload(message).build(),
                3000, // 超时时间
                delayLevel
            )
            logger.info { "延迟消息发送成功: ${message.id}, delayLevel: $delayLevel" }
        } catch (e: Exception) {
            logger.error(e) { "延迟消息发送失败: ${message.id}" }
        }
    }
    
    /**
     * 创建测试消息
     */
    fun createTestMessage(type: String, content: String, receiver: String? = null): GalaxyMessage {
        return GalaxyMessage(
            id = UUID.randomUUID().toString(),
            type = type,
            content = content,
            sender = "galaxy-business-worker",
            receiver = receiver,
            timestamp = LocalDateTime.now(),
            metadata = mapOf(
                "source" to "worker",
                "version" to "1.0"
            )
        )
    }
} 