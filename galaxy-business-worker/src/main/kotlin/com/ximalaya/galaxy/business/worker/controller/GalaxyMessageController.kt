package com.ximalaya.galaxy.business.worker.controller

import com.ximalaya.galaxy.business.common.message.GalaxyMessage
import com.ximalaya.galaxy.business.worker.service.GalaxyMessageProducer
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.swagger.annotations.ApiParam
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@Api(tags = ["Galaxy消息测试接口"])
@RestController
@RequestMapping("/api/galaxy/message")
class GalaxyMessageController {
    
    @Autowired
    private lateinit var galaxyMessageProducer: GalaxyMessageProducer
    
    @ApiOperation("发送同步消息")
    @PostMapping("/send/sync")
    fun sendSyncMessage(
        @ApiParam("消息类型") @RequestParam type: String,
        @ApiParam("消息内容") @RequestParam content: String,
        @ApiParam("接收者") @RequestParam(required = false) receiver: String?
    ): Map<String, Any> {
        val message = galaxyMessageProducer.createTestMessage(type, content, receiver)
        val success = galaxyMessageProducer.sendSyncMessage(message)
        
        return mapOf(
            "success" to success,
            "messageId" to message.id,
            "message" to message
        )
    }
    
    @ApiOperation("发送异步消息")
    @PostMapping("/send/async")
    fun sendAsyncMessage(
        @ApiParam("消息类型") @RequestParam type: String,
        @ApiParam("消息内容") @RequestParam content: String,
        @ApiParam("接收者") @RequestParam(required = false) receiver: String?
    ): Map<String, Any> {
        val message = galaxyMessageProducer.createTestMessage(type, content, receiver)
        galaxyMessageProducer.sendAsyncMessage(message)
        
        return mapOf(
            "success" to true,
            "messageId" to message.id,
            "message" to message
        )
    }
    
    @ApiOperation("发送延迟消息")
    @PostMapping("/send/delay")
    fun sendDelayMessage(
        @ApiParam("消息类型") @RequestParam type: String,
        @ApiParam("消息内容") @RequestParam content: String,
        @ApiParam("延迟级别(1-18)") @RequestParam delayLevel: Int,
        @ApiParam("接收者") @RequestParam(required = false) receiver: String?
    ): Map<String, Any> {
        val message = galaxyMessageProducer.createTestMessage(type, content, receiver)
        galaxyMessageProducer.sendDelayMessage(message, delayLevel)
        
        return mapOf(
            "success" to true,
            "messageId" to message.id,
            "delayLevel" to delayLevel,
            "message" to message
        )
    }
    
    @ApiOperation("批量发送消息")
    @PostMapping("/send/batch")
    fun sendBatchMessages(
        @ApiParam("消息数量") @RequestParam count: Int,
        @ApiParam("消息类型") @RequestParam type: String,
        @ApiParam("消息内容前缀") @RequestParam contentPrefix: String
    ): Map<String, Any> {
        val results = mutableListOf<Map<String, Any>>()
        
        repeat(count) { index ->
            val content = "$contentPrefix-${index + 1}"
            val message = galaxyMessageProducer.createTestMessage(type, content)
            val success = galaxyMessageProducer.sendSyncMessage(message)
            
            results.add(mapOf(
                "index" to (index + 1),
                "success" to success,
                "messageId" to message.id
            ))
        }
        
        val successCount = results.count { it["success"] as Boolean }
        
        return mapOf(
            "total" to count,
            "successCount" to successCount,
            "failCount" to (count - successCount),
            "results" to results
        )
    }
    
    @ApiOperation("获取消息主题信息")
    @GetMapping("/topic/info")
    fun getTopicInfo(): Map<String, Any> {
        return mapOf(
            "topic" to GalaxyMessage.TOPIC,
            "producerGroup" to GalaxyMessage.PRODUCER_GROUP,
            "consumerGroup" to GalaxyMessage.CONSUMER_GROUP
        )
    }
} 