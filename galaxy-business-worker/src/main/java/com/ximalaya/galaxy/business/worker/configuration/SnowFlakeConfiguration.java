package com.ximalaya.galaxy.business.worker.configuration;

import com.ximalaya.galaxy.business.common.support.SnowFlake;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Random;

/**
 * 雪花算法
 *
 * <AUTHOR>
 * @create 2022-06-05 13:13
 */
@Configuration
public class SnowFlakeConfiguration {

  private final Random random = new Random();

  @Bean
  public SnowFlake snowFlake() {
    int dataCenterId = random.nextInt((int) SnowFlake.MAX_DATA_CENTER_NUM);
    int machineId = random.nextInt((int) SnowFlake.MAX_MACHINE_NUM);
    return new SnowFlake(dataCenterId, machineId);
  }

}
