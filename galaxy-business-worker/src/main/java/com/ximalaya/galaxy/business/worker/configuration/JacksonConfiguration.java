package com.ximalaya.galaxy.business.worker.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.ximalaya.galaxy.business.common.support.DateTimes;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Configuration
@AutoConfigureBefore(WebMvcAutoConfiguration.class)
public class JacksonConfiguration {

  @Bean
  @Primary
  public ObjectMapper jacksonObjectMapper(Jackson2ObjectMapperBuilder builder) {
    return builder.createXmlMapper(false)
        .serializerByType(LocalDateTime.class,
            new LocalDateTimeSerializer(DateTimes.YEAR_MONTH_DAY_HOUR_MINUTE_SECOND))
        .deserializerByType(LocalDateTime.class,
            new LocalDateTimeDeserializer(DateTimes.YEAR_MONTH_DAY_HOUR_MINUTE_SECOND))
        .serializerByType(LocalDate.class, new LocalDateSerializer(DateTimes.YEAR_MONTH_DAY))
        .deserializerByType(LocalDate.class, new LocalDateDeserializer(DateTimes.YEAR_MONTH_DAY))
        .build();
  }

  @Bean
  public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter(
      ObjectMapper objectMapper) {
    return new MappingJackson2HttpMessageConverter(objectMapper);
  }

}
