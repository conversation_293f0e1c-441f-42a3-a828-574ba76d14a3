package com.ximalaya.galaxy.business.worker.configuration;

import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson
 *
 * <AUTHOR>
 * @create 2022-01-13 21:39
 */
@Configuration
@AutoConfigureAfter(RedisAutoConfiguration.class)
public class RedissonConfiguration {

    @Bean(destroyMethod = "shutdown")
    public RedissonClient singleRedisson(RedisProperties redisProperties) {
        Config redisConfig = new Config();
        SingleServerConfig singleServerConfig = redisConfig.useSingleServer();

        singleServerConfig.setAddress("redis://" + redisProperties.getHost() + ":" + redisProperties.getPort())
                .setDatabase(redisProperties.getDatabase())
                .setConnectionPoolSize(10)
                .setConnectionMinimumIdleSize(5)
                .setConnectTimeout(10000)
                .setTimeout(10000);
        if(StringUtils.isNotEmpty(redisProperties.getPassword())) {
            singleServerConfig.setPassword(redisProperties.getPassword());
        }

        return Redisson.create(redisConfig);
    }

}
