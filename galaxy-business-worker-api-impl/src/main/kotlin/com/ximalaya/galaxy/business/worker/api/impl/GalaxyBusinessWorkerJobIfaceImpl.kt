package com.ximalaya.galaxy.business.worker.api.impl

import com.ximalaya.eros.mainstay.context.annotation.MainstayServer
import com.ximalaya.galaxy.business.business.worker.BizGalaxyWorker
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.worker.api.converter.*
import com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerJobService
import com.ximalaya.galaxy.business.worker.api.thrift.entity.*

/**
 *<AUTHOR>
 *@create 2024-11-28 11:58
 */
@MainstayServer(group = "galaxy-business-worker")
open class GalaxyBusinessWorkerJobIfaceImpl(
    private val bizGalaxyWorker: BizGalaxyWorker
) : GalaxyBusinessWorkerJobService.Iface {

    override fun startSessionJob(startRequest: StartSessionJobRequest?): CommonJobResponse {
        val request = StartSessionJobRequestConverter.transform(startRequest)

        return doJob {
            CommonJobResponseConverter.transform(bizGalaxyWorker.runSessionJob(request))
        }
    }

    override fun startToolJob(startRequest: StartToolJobRequest?): CommonJobResponse {
        val request = StartToolJobRequestConverter.transform(startRequest)

        return doJob {
            CommonJobResponseConverter.transform(bizGalaxyWorker.runToolJob(request))
        }
    }

    override fun createSessionJob(createRequest: CreateSessionJobRequest?): CommonJobResponse {
        val request = CreateSessionJobRequestConverter.transform(createRequest)

        return doJob {
            CommonJobResponseConverter.transform(bizGalaxyWorker.createAndRunSessionJob(request))
        }
    }

    override fun createAndStartSessionJob(startRequest: CreateAndStartPhaseJobRequest?): CommonJobResponse {
        val request = CreateAndStartPhaseJobRequestConverter.transform(startRequest)

        return doJob {
            CommonJobResponseConverter.transform(bizGalaxyWorker.createAndRunSessionJob(request))
        }
    }

    override fun createAndStartToolJob(startRequest: CreateAndStartToolJobRequest?): CommonJobResponse {
        val request = CreateAndStartToolJobRequestConverter.transform(startRequest)

        return doJob {
            CommonJobResponseConverter.transform(bizGalaxyWorker.createAndRunToolJob(request))
        }
    }

    private fun doJob(task: () -> CommonJobResponse): CommonJobResponse {
        val response = com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse()

        try {
            return task.invoke()
        } catch (ex: GalaxyException) {
            val errorCode = ex.errorCode ?: ErrorCode.UNKNOWN_ERROR
            val message = ex.message ?: errorCode.message

            response.code = errorCode.code
            response.message = message
            return CommonJobResponseConverter.transform(response)
        }
    }

}