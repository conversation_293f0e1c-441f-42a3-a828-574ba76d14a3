package com.ximalaya.galaxy.business.business.impl

import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.vo.*
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import com.ximalaya.galaxy.business.repo.entity.AlbumEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.entity.TrackEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.service.AlbumService
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import com.ximalaya.galaxy.business.repo.service.TrackService
import org.apache.commons.collections4.CollectionUtils
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 10:27
 */
@BusinessComponent
class BizAlbumTrackImpl(
    private val sessionService: GalaxySessionService,
    private val phaseService: GalaxyPhaseService,
    private val albumService: AlbumService,
    private val trackService: TrackService,
) : BizAlbumTrack {

    override fun getAlbums(uid: Long, pageRequest: QueryPageRequestVo): GalaxyPage<AlbumVo?> {
        val albums = albumService.ktQuery()
            .eq(AlbumEntity::uid, uid)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .orderByDesc(AlbumEntity::createTime)
            .page(pageRequest.toPageInfo())

        return GalaxyPage.of(albums).map {
            AlbumVo.of(it)
        }
    }

    override fun getAlbum(uid: Long, sessionCode: Long): AlbumVo? {
        val album = albumService.ktQuery()
            .eq(AlbumEntity::sessionCode, sessionCode)
            .eq(AlbumEntity::uid, uid)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()

        return album?.let { AlbumVo.of(it) }
    }

    override fun getAlbumTrack(uid: Long, sessionCode: Long): AlbumTrackVo? {
        val album = albumService.ktQuery()
            .eq(AlbumEntity::sessionCode, sessionCode)
            .eq(AlbumEntity::uid, uid)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()

        if (album == null) {
            return null
        }
        val tracks = trackService.ktQuery()
            .eq(TrackEntity::albumId, album.id)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        val trackPhaseCodes = tracks
            .filter { it.phaseCode != null }
            .map { it.phaseCode!! }

        val phaseCodePhaseMapping = phaseService.selectByPhaseCodes(trackPhaseCodes)
            .associateBy { it.phaseCode!! }

        return AlbumTrackVo().apply {
            this.album = AlbumVo.of(album)
            this.tracks = tracks?.map {
                TrackVo.of(it, phaseCodePhaseMapping[it.phaseCode])
            }?.sortedBy { it.index }
        }
    }

    @Transactional(rollbackFor = [Throwable::class])
    override fun saveAgentAlbum(
        sessionCode: Long,
        saveVo: SaveAgentAlbumVo,
    ) {
        val session = sessionService.selectBySessionCode(sessionCode) ?: throw GalaxyException(
            ErrorCode.CONTENT_NOT_FOUND,
            "会话不存在"
        )
        val uid = session.uid!!
        val now = LocalDateTime.now()

        val album = albumService.selectBySessionCode(sessionCode)
        if (album == null) {
            val albumEntity = AlbumEntity().apply {
                this.uid = uid
                this.sessionCode = sessionCode
                this.albumName = saveVo.albumName
                this.deletedAt = LogicDeleted.SAVE.getCode()
                this.createTime = now
                this.updateTime = now
            }

            GalaxyAsserts.assertTrue(
                albumService.save(albumEntity),
                ErrorCode.ALBUM_ERROR,
                "创建专辑失败"
            )

            // 重新插入
            val trackEntities = saveVo.tracks!!.map {
                it.toEntity(albumEntity.id!!, uid, sessionCode, now)
            }

            GalaxyAsserts.assertTrue(trackService.saveBatch(trackEntities), ErrorCode.ALBUM_ERROR, "创建专辑失败")
            return
        }

        // 开始更新
        GalaxyAsserts.assertTrue(
            albumService.updateAlbum(album.id!!, saveVo.albumName),
            ErrorCode.ALBUM_ERROR,
            "更新专辑失败"
        )

        // 对比声音
        val newSourceIdTrackMapping = saveVo.tracks!!.associateBy { it.sourceId }
        val oldTracks = trackService.selectBySessionCode(sessionCode)

        // 找出不允许被删除的声音
        val mustNotUpdateTrackSourceIds = oldTracks.filter { old ->
            old.phaseCode != null
        }.map {
            it.sourceId
        }.toSet()

        // 需要删除的
        val needRemoveTracks = oldTracks.filter { old ->
            newSourceIdTrackMapping.none { old.sourceId == it.key }
        }

        // 存在就报错
        val mustNotRemoveTracks = needRemoveTracks.filter {
            mustNotUpdateTrackSourceIds.contains(it.sourceId)
        }
        if (CollectionUtils.isNotEmpty(mustNotRemoveTracks)) {
            throw GalaxyException(
                ErrorCode.ALBUM_ERROR,
                "以下id: ${mustNotRemoveTracks.map { it.sourceId }} 已经开始制作声音了不允许删除"
            )
        }

        // 需要更新的
        val needUpdateTracks = oldTracks.filter { old ->
            newSourceIdTrackMapping.any { old.sourceId == it.key }
        }
        // 存在就报错
        val mustNotUpdateTracks = needUpdateTracks.filter {
            val saveTrackVo = newSourceIdTrackMapping[it.sourceId] ?: throw GalaxyException(
                ErrorCode.ALBUM_ERROR,
                "声音: ${it.sourceId} 不存在"
            )
            !saveTrackVo.diffTrackName(it)
        }.filter {
            mustNotUpdateTrackSourceIds.contains(it.sourceId)
        }
        if (CollectionUtils.isNotEmpty(mustNotUpdateTracks)) {
            throw GalaxyException(
                ErrorCode.ALBUM_ERROR,
                "以下id: ${mustNotRemoveTracks.map { it.sourceId }} 已经开始制作声音了不允许更新"
            )
        }

        // 需要新增的
        val needAddTracks = saveVo.tracks!!.filter { old ->
            oldTracks.none { old.sourceId == it.sourceId }
        }

        // 删除
        if (CollectionUtils.isNotEmpty(needRemoveTracks)) {
            GalaxyAsserts.assertTrue(
                trackService.removeBatchByIds(needRemoveTracks.map { it.id!! }),
                ErrorCode.ALBUM_ERROR,
                "更新专辑失败"
            )
        }
        // 更新
        if (CollectionUtils.isNotEmpty(needUpdateTracks)) {

            GalaxyAsserts.assertTrue(
                trackService.updateBatchById(needUpdateTracks.map { old ->
                    val new = saveVo.tracks!!.first { old.sourceId == it.sourceId }
                    old.apply {
                        this.trackIndex = new.index
                        this.parentTitle = new.parentTitle
                        this.trackName = new.trackName
                        this.updateTime = now
                    }
                }),
                ErrorCode.ALBUM_ERROR,
                "更新专辑失败"
            )
        }

        // 新增
        if (CollectionUtils.isNotEmpty(needAddTracks)) {
            val trackEntities = needAddTracks.map {
                it.toEntity(album.id!!, uid, sessionCode, now)
            }
            GalaxyAsserts.assertTrue(
                trackService.saveBatch(trackEntities),
                ErrorCode.ALBUM_ERROR,
                "更新专辑失败"
            )
        }

    }

    override fun getAgentAlbum(sessionCode: Long): AgentAlbumVo? {
        val album = albumService.ktQuery()
            .eq(AlbumEntity::sessionCode, sessionCode)
            .eq(AlbumEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()

        if (album == null) {
            return null
        }

        val tracks = trackService.ktQuery()
            .eq(TrackEntity::albumId, album.id)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        return AgentAlbumVo.of(album, tracks)
    }

    @Transactional(rollbackFor = [Throwable::class])
    override fun removeAlbum(uid: Long, sessionCode: Long): Boolean {
        val album = getAlbum(uid, sessionCode) ?: throw GalaxyException(ErrorCode.CONTENT_NOT_FOUND, "专辑不存在")

        // 1. 删除会话
        GalaxyAsserts.assertTrue(
            phaseService.deleteNotOutlineBySessionCode(sessionCode),
            ErrorCode.ALBUM_ERROR,
            "更新专辑失败"
        )
        // 2. 删除现存所有的音轨
        GalaxyAsserts.assertTrue(
            trackService.deleteBySessionCode(sessionCode),
            ErrorCode.ALBUM_ERROR,
            "更新专辑失败"
        )

        return albumService.deleteAlbum(album.id!!)
    }

    override fun getTracks(uid: Long, sessionCode: Long): List<TrackVo>? {
        val album = getAlbum(uid, sessionCode) ?: return emptyList()

        val tracks = trackService.ktQuery()
            .eq(TrackEntity::albumId, album.id)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        val trackPhaseCodes = tracks
            .filter { it.phaseCode != null }
            .map { it.phaseCode!! }

        val phaseCodePhaseMapping = phaseService.selectByPhaseCodes(trackPhaseCodes)
            .associateBy { it.phaseCode!! }

        return tracks.map {
            TrackVo.of(it, phaseCodePhaseMapping[it.phaseCode])
        }
    }

    override fun getTracks(uid: Long, sessionCode: Long, pageRequest: QueryPageRequestVo): GalaxyPage<TrackVo?> {
        val album = getAlbum(uid, sessionCode) ?: return GalaxyPage()

        val tracks = trackService.ktQuery()
            .eq(TrackEntity::albumId, album.id)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .page(pageRequest.toPageInfo())

        val trackPhaseCodes = tracks.records
            .filter { it.phaseCode != null }
            .map { it.phaseCode!! }

        val phaseCodePhaseMapping = phaseService.selectByPhaseCodes(trackPhaseCodes)
            .associateBy { it.phaseCode!! }

        return GalaxyPage.of(tracks).map {
            TrackVo.of(it, phaseCodePhaseMapping[it.phaseCode])
        }
    }

    override fun getTrack(uid: Long, sessionCode: Long, sourceId: String): TrackVo? {
        val track = trackService.ktQuery()
            .eq(TrackEntity::sessionCode, sessionCode)
            .eq(TrackEntity::sourceId, sourceId)
            .eq(TrackEntity::uid, uid)
            .eq(TrackEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .one()

        val phase = track.phaseCode?.let {
            phaseService.selectBySessionCodeAndPhaseCode(sessionCode, it)
        }

        return track?.let { TrackVo.of(it, phase) }
    }

    @Transactional(rollbackFor = [Throwable::class])
    override fun updateTrack(
        uid: Long,
        sessionCode: Long,
        phaseCode: Long,
        saveVo: SaveTrackContentVo,
        checkSessionUid: (Long, GalaxySessionEntity) -> Unit
    ) {
        val session = sessionService.selectBySessionCode(sessionCode) ?: throw GalaxyException(
            ErrorCode.CONTENT_NOT_FOUND,
            "会话不存在"
        )
        checkSessionUid(uid, session)

        getAlbum(uid, sessionCode) ?: throw GalaxyException(ErrorCode.CONTENT_NOT_FOUND, "专辑不存在")

        val track = trackService.selectBySessionCodeAndPhaseCode(sessionCode, phaseCode)
        GalaxyAsserts.assertNotNull(track, ErrorCode.TRACK_ERROR, "声音不存在")
        GalaxyAsserts.assertTrue(
            trackService.updateTrack(track!!.id!!, saveVo.trackName, saveVo.trackContent),
            ErrorCode.TRACK_ERROR,
            "更新声音失败"
        )
    }

    override fun saveTrackPhaseCode(id: Long, phaseCode: Long): Boolean {
        return trackService.ktUpdate()
            .eq(TrackEntity::id, id)
            .set(TrackEntity::phaseCode, phaseCode)
            .set(TrackEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun removeTrack(uid: Long, sessionCode: Long, phaseName: String): Boolean {
        val track =
            getTrack(uid, sessionCode, phaseName) ?: throw GalaxyException(ErrorCode.TRACK_ERROR, "声音不存在")

        GalaxyAsserts.assertNull(track.phaseCode, ErrorCode.TRACK_ERROR, "声音已开启制作不允许删除")

        val phase = phaseService.selectBySessionCodeAndPhaseName(sessionCode, phaseName)
        GalaxyAsserts.assertNull(phase, ErrorCode.TRACK_ERROR, "声音已开启制作不允许删除")

        return trackService.deleteTrack(track.id!!)
    }

    override fun renameTrack(uid: Long, vo: RenameTrackVo): Boolean {
        val track =
            getTrack(uid, vo.sessionCode!!.toLong(), vo.sourceId!!) ?: throw GalaxyException(
                ErrorCode.TRACK_ERROR,
                "声音不存在"
            )

        return trackService.ktUpdate()
            .eq(TrackEntity::id, track.id)
            .set(TrackEntity::trackName, vo.trackName)
            .set(TrackEntity::updateTime, LocalDateTime.now())
            .update()
    }

}