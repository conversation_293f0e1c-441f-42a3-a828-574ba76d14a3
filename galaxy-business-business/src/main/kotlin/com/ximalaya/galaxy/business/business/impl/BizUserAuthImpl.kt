package com.ximalaya.galaxy.business.business.impl

import com.ximalaya.galaxy.business.business.BizUserAuth
import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.support.InvitationCodeGenerator
import com.ximalaya.galaxy.business.business.vo.LoginRequestVo
import com.ximalaya.galaxy.business.business.vo.LoginResponseVo
import com.ximalaya.galaxy.business.business.vo.RegisterRequestVo
import com.ximalaya.galaxy.business.business.vo.UserInfoVo
import com.ximalaya.galaxy.business.business.vo.InvitationRelationVo
import com.ximalaya.galaxy.business.business.vo.InvitedUserVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.common.support.JwtHelper
import com.ximalaya.galaxy.business.common.support.PasswordHelper
import com.ximalaya.galaxy.business.repo.entity.InvitationCodeEntity
import com.ximalaya.galaxy.business.repo.entity.UserEntity
import com.ximalaya.galaxy.business.repo.entity.UserLoginLogEntity
import com.ximalaya.galaxy.business.repo.entity.UserTokenEntity
import com.ximalaya.galaxy.business.repo.enums.InvitationCodeState
import com.ximalaya.galaxy.business.repo.service.InvitationCodeService
import com.ximalaya.galaxy.business.repo.service.UserLoginLogService
import com.ximalaya.galaxy.business.repo.service.UserService
import com.ximalaya.galaxy.business.repo.service.UserTokenService
import mu.KotlinLogging
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 用户认证业务实现类
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
@BusinessComponent
class BizUserAuthImpl(
    private val userService: UserService,
    private val userTokenService: UserTokenService,
    private val invitationCodeService: InvitationCodeService,
    private val userLoginLogService: UserLoginLogService,
    private val passwordHelper: PasswordHelper,
    private val jwtHelper: JwtHelper
) : BizUserAuth {
    
    companion object {
        private val logger = KotlinLogging.logger { }
    }
    
    @Transactional(rollbackFor = [Throwable::class])
    override fun register(request: RegisterRequestVo): Boolean {
        // 验证邀请码
        val invitationCode = invitationCodeService.ktQuery()
            .eq(InvitationCodeEntity::code, request.invitationCode)
            .eq(InvitationCodeEntity::codeState, InvitationCodeState.UNUSED.code)
            .one()
        
        GalaxyAsserts.assertNotNull(invitationCode, ErrorCode.PARAMS_ERROR, "邀请码不存在或已被使用")
        
        // 检查邀请码是否过期
        if (invitationCode.expireTime != null && invitationCode.expireTime!!.isBefore(LocalDateTime.now())) {
            throw GalaxyException(ErrorCode.PARAMS_ERROR, "邀请码已过期")
        }
        
        // 检查用户名是否已存在
        GalaxyAsserts.assertFalse(
            userService.existsByUsername(request.username),
            ErrorCode.PARAMS_ERROR,
            "用户名已存在"
        )
        
        // 创建用户
        val user = UserEntity.of(
            username = request.username,
            password = passwordHelper.encode(request.password),
            nickname = request.nickname,
            email = request.email,
            phone = request.phone,
            permissionId = invitationCode.permissionId ?: 0,
            invitationCode = request.invitationCode,
            inviterId = invitationCode.inviterId
        )
        
        val saveResult = userService.save(user)
        GalaxyAsserts.assertTrue(saveResult, ErrorCode.ASSERT_ERROR, "用户注册失败")
        
        // 使用邀请码（一次性使用，立即失效）
        GalaxyAsserts.assertTrue(
            invitationCodeService.ktUpdate()
                .eq(InvitationCodeEntity::code, request.invitationCode)
                .set(InvitationCodeEntity::uid, user.id)
                .set(InvitationCodeEntity::codeState, InvitationCodeState.USED.code)
                .set(InvitationCodeEntity::updateTime, LocalDateTime.now())
                .update(),
            ErrorCode.ASSERT_ERROR,
            "邀请码使用失败"
        )
        
        logger.info("用户注册成功: username=${request.username}, userId=${user.id}, inviterId=${invitationCode.inviterId}")
        return true
    }
    
    override fun login(request: LoginRequestVo, ipAddress: String?, deviceInfo: String?): LoginResponseVo? {
        // 查找用户
        val user = userService.findByUsername(request.username)
        if (user == null || user.status != 1) {
            logLoginFailure(request.username, ipAddress, deviceInfo, "用户不存在或已被禁用")
            return null
        }
        
        // 验证密码
        if (!passwordHelper.matches(request.password, user.password!!)) {
            logLoginFailure(request.username, ipAddress, deviceInfo, "密码错误")
            return null
        }
        
        // 生成token
        val token = jwtHelper.generateToken(user.id!!, user.username!!, user.permissionId!!)
        val refreshToken = jwtHelper.generateRefreshToken(user.id!!)
        
        // 保存token到数据库
        val tokenEntity = UserTokenEntity.of(
            userId = user.id!!,
            token = token,
            refreshToken = refreshToken,
            expireTime = LocalDateTime.now().plusSeconds(86400), // 24小时
            refreshExpireTime = LocalDateTime.now().plusSeconds(604800), // 7天
            deviceInfo = deviceInfo,
            ipAddress = ipAddress
        )
        
        userTokenService.save(tokenEntity)
        
        // 更新最后登录信息
        userService.updateLastLoginInfo(user.id!!, LocalDateTime.now(), ipAddress)
        
        // 记录登录日志
        logLoginSuccess(user.id!!, ipAddress, deviceInfo)
        
        logger.info("用户登录成功: username=${request.username}, userId=${user.id}")
        
        return LoginResponseVo(
            token = token,
            refreshToken = refreshToken,
            userId = user.id!!,
            username = user.username!!,
            nickname = user.nickname,
            permissionId = user.permissionId!!
        )
    }
    
    override fun logout(token: String): Boolean {
        return userTokenService.invalidateToken(token)
    }
    
    override fun refreshToken(refreshToken: String): LoginResponseVo? {
        // 验证刷新token
        if (!jwtHelper.validateToken(refreshToken) || jwtHelper.isTokenExpired(refreshToken)) {
            return null
        }
        
        val userId = jwtHelper.getUserIdFromToken(refreshToken)
        if (userId == null) {
            return null
        }
        
        // 查找用户
        val user = userService.findById(userId)
        if (user == null || user.status != 1) {
            return null
        }
        
        // 生成新的token
        val newToken = jwtHelper.generateToken(user.id!!, user.username!!, user.permissionId!!)
        val newRefreshToken = jwtHelper.generateRefreshToken(user.id!!)
        
        // 保存新token
        val tokenEntity = UserTokenEntity.of(
            userId = user.id!!,
            token = newToken,
            refreshToken = newRefreshToken,
            expireTime = LocalDateTime.now().plusSeconds(86400), // 24小时
            refreshExpireTime = LocalDateTime.now().plusSeconds(604800) // 7天
        )
        
        userTokenService.save(tokenEntity)
        
        return LoginResponseVo(
            token = newToken,
            refreshToken = newRefreshToken,
            userId = user.id!!,
            username = user.username!!,
            nickname = user.nickname,
            permissionId = user.permissionId!!
        )
    }
    
    override fun getUserInfo(userId: Long): UserInfoVo? {
        val user = userService.findById(userId) ?: return null
        
        return UserInfoVo(
            id = user.id!!,
            username = user.username!!,
            nickname = user.nickname,
            email = user.email,
            phone = user.phone,
            avatar = user.avatar,
            permissionId = user.permissionId!!,
            lastLoginTime = user.lastLoginTime,
            lastLoginIp = user.lastLoginIp
        )
    }
    
    override fun generateInvitationCode(count: Int, permissionId: Int, description: String?, creatorId: Long?): Boolean {
        if (count < 1) {
            return true
        }
        
        val existCodes = invitationCodeService.ktQuery().list()
            .map { it.code!! }
            .toMutableSet()
        
        val codes = InvitationCodeGenerator.generate(count, existCodes)
        
        val entities = codes.map { code ->
            InvitationCodeEntity().apply {
                this.code = code
                this.codeState = InvitationCodeState.UNUSED.code
                this.permissionId = permissionId
                this.description = description
                this.creatorId = creatorId
                this.createTime = LocalDateTime.now()
                this.updateTime = LocalDateTime.now()
            }
        }
        
        return invitationCodeService.saveBatch(entities)
    }
    
    override fun generatePersonalInvitationCode(userId: Long, permissionId: Int, description: String?): String? {
        // 检查用户是否存在
        val user = userService.findById(userId) ?: return null
        
        // 获取现有邀请码
        val existCodes = invitationCodeService.ktQuery().list()
            .map { it.code!! }
            .toMutableSet()
        
        // 生成单个邀请码
        val code = InvitationCodeGenerator.generateSingle(existCodes)
        
        // 创建邀请码实体
        val invitationCode = InvitationCodeEntity().apply {
            this.code = code
            this.codeState = InvitationCodeState.UNUSED.code
            this.permissionId = permissionId
            this.description = description
            this.creatorId = userId
            this.inviterId = userId
            this.createTime = LocalDateTime.now()
            this.updateTime = LocalDateTime.now()
        }
        
        val saveResult = invitationCodeService.save(invitationCode)
        return if (saveResult) code else null
    }
    
    override fun validateInvitationCode(code: String): Boolean {
        val invitationCode = invitationCodeService.ktQuery()
            .eq(InvitationCodeEntity::code, code)
            .eq(InvitationCodeEntity::codeState, InvitationCodeState.UNUSED.code)
            .one()
        
        if (invitationCode == null) {
            return false
        }
        
        // 检查邀请码是否过期
        if (invitationCode.expireTime != null && invitationCode.expireTime!!.isBefore(LocalDateTime.now())) {
            return false
        }
        
        return true
    }
    
    override fun getUserInvitationRelation(userId: Long): InvitationRelationVo? {
        val user = userService.findById(userId) ?: return null
        
        // 获取邀请人信息
        val inviter = user.inviterId?.let { userService.findById(it) }
        
        // 获取被邀请的用户列表
        val invitedUsers = userService.ktQuery()
            .eq(UserEntity::inviterId, userId)
            .list()
            .map { invitedUser: UserEntity ->
                InvitedUserVo(
                    userId = invitedUser.id!!,
                    username = invitedUser.username!!,
                    nickname = invitedUser.nickname,
                    createTime = invitedUser.createTime
                )
            }
        
        return InvitationRelationVo(
            userId = user.id!!,
            username = user.username!!,
            nickname = user.nickname,
            invitationCode = user.invitationCode,
            inviterId = user.inviterId,
            inviterUsername = inviter?.username,
            inviterNickname = inviter?.nickname,
            invitedUsers = invitedUsers,
            createTime = user.createTime
        )
    }
    
    private fun logLoginSuccess(userId: Long, ipAddress: String?, deviceInfo: String?) {
        val logEntity = UserLoginLogEntity.of(
            userId = userId,
            loginTime = LocalDateTime.now(),
            loginIp = ipAddress,
            deviceInfo = deviceInfo,
            status = 1
        )
        userLoginLogService.save(logEntity)
    }
    
    private fun logLoginFailure(username: String, ipAddress: String?, deviceInfo: String?, failReason: String) {
        val logEntity = UserLoginLogEntity.of(
            userId = 0L, // 登录失败时用户ID为0
            loginTime = LocalDateTime.now(),
            loginIp = ipAddress,
            deviceInfo = deviceInfo,
            status = 0,
            failReason = failReason
        )
        userLoginLogService.save(logEntity)
    }
} 