package com.ximalaya.galaxy.business.business

import com.ximalaya.galaxy.business.service.vo.AgentBlockChatRequestVo
import com.ximalaya.galaxy.business.service.vo.AgentBlockResponseVo
import com.ximalaya.galaxy.business.service.vo.AgentChatRequestVo
import okhttp3.Callback
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 *<AUTHOR>
 *@create 2025-05-21 16:45
 */
interface BizAgent {

  fun hello(emitter: SseEmitter)

  fun streamChat(vo: AgentChatRequestVo, callback: Callback)

  fun blockChat(vo: AgentBlockChatRequestVo): AgentBlockResponseVo

}