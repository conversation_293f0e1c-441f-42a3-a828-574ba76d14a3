package com.ximalaya.galaxy.business.business.vo

import com.ximalaya.galaxy.business.repo.entity.AlbumEntity
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 16:59
 */
@ApiModel(description = "专辑VO")
class AlbumVo {

    @ApiModelProperty("专辑ID")
    var id: Long? = null

    @ApiModelProperty("uid")
    var uid: Long? = null

    @ApiModelProperty("sessionCode")
    var sessionCode: String? = null

    @ApiModelProperty("专辑名称")
    var albumName: String? = null

    var createTime: LocalDateTime? = null
    var updateTime: LocalDateTime? = null

    companion object {

        fun of(entity: AlbumEntity): AlbumVo {
            return AlbumVo().apply {
                this.id = entity.id
                this.uid = entity.uid
                this.sessionCode = entity.sessionCode?.toString()
                this.albumName = entity.albumName
                this.createTime = entity.createTime
                this.updateTime = entity.updateTime
            }
        }

    }

}