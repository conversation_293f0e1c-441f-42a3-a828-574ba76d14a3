package com.ximalaya.galaxy.business.business.vo

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import javax.validation.constraints.NotBlank

/**
 *<AUTHOR>
 *@create 2025-06-12 17:24
 */
@ApiModel(description = "保存声音内容VO")
class SaveTrackContentVo {

    @ApiModelProperty("声音名称")
    var trackName: String? = null

    @ApiModelProperty("声音内容")
    @NotBlank(message = "声音内容不能为空哦~")
    var trackContent: String?=null

}