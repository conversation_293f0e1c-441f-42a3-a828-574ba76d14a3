package com.ximalaya.galaxy.business.business.support

import java.util.*

/**
 * 邀请码生成器
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
object InvitationCodeGenerator {
    
    private const val CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    private const val CODE_LENGTH = 8
    
    /**
     * 生成邀请码
     */
    fun generate(count: Int, existCodes: Set<String>): List<String> {
        val codes = mutableListOf<String>()
        val random = Random()
        
        while (codes.size < count) {
            val code = generateCode(random)
            if (!existCodes.contains(code) && !codes.contains(code)) {
                codes.add(code)
            }
        }
        
        return codes
    }
    
    /**
     * 生成单个邀请码
     */
    fun generateSingle(existCodes: Set<String>): String {
        val random = Random()
        var code: String
        do {
            code = generateCode(random)
        } while (existCodes.contains(code))
        return code
    }
    
    private fun generateCode(random: Random): String {
        val sb = StringBuilder()
        repeat(CODE_LENGTH) {
            sb.append(CHARS[random.nextInt(CHARS.length)])
        }
        return sb.toString()
    }
}