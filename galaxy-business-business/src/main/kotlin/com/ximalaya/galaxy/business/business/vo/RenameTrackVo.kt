package com.ximalaya.galaxy.business.business.vo

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import javax.validation.constraints.NotBlank

/**
 *<AUTHOR>
 *@create 2025-06-12 17:24
 */
@ApiModel(description = "重命名声音VO")
class RenameTrackVo {

    @ApiModelProperty("sessionCode")
    @NotBlank(message = "sessionCode不能为空哦")
    var sessionCode: String? = null

    @ApiModelProperty("sourceId")
    @NotBlank(message = "sourceId不能为空哦")
    var sourceId: String? = null

    @ApiModelProperty("声音名称")
    @NotBlank(message = "声音名称不能为空哦")
    var trackName: String? = null

}