package com.ximalaya.galaxy.business.business.vo

import javax.validation.constraints.Email
import javax.validation.constraints.NotBlank
import javax.validation.constraints.Pattern
import javax.validation.constraints.Size


/**
 * 注册请求VO
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
data class RegisterRequestVo(
    @field:NotBlank(message = "用户名不能为空")
    @field:Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    @field:Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    val username: String,
    
    @field:NotBlank(message = "密码不能为空")
    @field:Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    val password: String,
    
    @field:NotBlank(message = "邀请码不能为空")
    val invitationCode: String,
    
    val nickname: String? = null,
    
    @field:Email(message = "邮箱格式不正确")
    val email: String? = null,
    
    @field:Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    val phone: String? = null
) 