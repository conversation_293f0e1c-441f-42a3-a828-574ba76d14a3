package com.ximalaya.galaxy.business.business.impl

import com.ximalaya.galaxy.business.business.BizDify
import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.vo.DirectBlockingCallDifyVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.service.DifyService
import com.ximalaya.galaxy.business.service.configuration.props.GalaxyProperties
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo

/**
 *<AUTHOR>
 *@create 2025-06-26 22:51
 */
@BusinessComponent
class BizDifyImpl(
    private val difyService: DifyService,
    private val galaxyProperties: GalaxyProperties,
) : BizDify {

    override fun regenerateParentTitle(uid: Long, vo: DirectBlockingCallDifyVo): Map<String, String>? {
        val appSecret = galaxyProperties.dify?.getRegenerateParentTitle() ?: throw GalaxyException(
            ErrorCode.CONTENT_NOT_FOUND,
            "appSecret不存在"
        )
        val requestVo = vo.toDifyRequestVo(appSecret, uid)
        return doCall(requestVo)
    }

    override fun regenerateTrackName(uid: Long, vo: DirectBlockingCallDifyVo): Map<String, String>? {
        val appSecret = galaxyProperties.dify?.getRegenerateTrackName() ?: throw GalaxyException(
            ErrorCode.CONTENT_NOT_FOUND,
            "appSecret不存在"
        )
        val requestVo = vo.toDifyRequestVo(appSecret, uid)
        return doCall(requestVo)
    }

    private fun doCall(requestVo: DifyRequestVo): Map<String, String>? {
        val difyResponseVo = difyService.blockingCall(requestVo)
        return checkAndExtractDifyResponse(difyResponseVo)
    }

    private fun checkAndExtractDifyResponse(difyResponse: DifyResponseVo): Map<String, String>? {
        GalaxyAsserts.assertTrue(difyResponse.isSuccess(), ErrorCode.CALL_DIFY_ERROR, "流程发生错误")

        val result: Map<String, String>? = difyResponse.data?.outputs
        GalaxyAsserts.assertNotEmpty(result, ErrorCode.CALL_DIFY_ERROR, "没有生产处结果")

        return result
    }
}