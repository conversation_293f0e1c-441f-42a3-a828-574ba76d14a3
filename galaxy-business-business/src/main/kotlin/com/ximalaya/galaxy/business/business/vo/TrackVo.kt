package com.ximalaya.galaxy.business.business.vo

import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.TrackEntity
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-06-02 16:59
 */
@ApiModel(description = "声音VO")
class TrackVo {

    @ApiModelProperty("声音ID")
    var id: Long? = null

    @ApiModelProperty("sourceId")
    var sourceId: String? = null

    @ApiModelProperty("专辑ID")
    var albumId: Long? = null

    @ApiModelProperty("uid")
    var uid: Long? = null

    @ApiModelProperty("sessionCode")
    var sessionCode: String? = null

    @ApiModelProperty("phaseCode")
    var phaseCode: String? = null

    @ApiModelProperty("父标题")
    var parentTitle: String? = null

    @ApiModelProperty("索引")
    var index: Int? = null

    @ApiModelProperty("声音名称")
    var trackName: String? = null

    @ApiModelProperty("声音内容")
    var trackContent: String? = null

    @ApiModelProperty("声音状态")
    var trackStatus: String? = null

    var createTime: LocalDateTime? = null
    var updateTime: LocalDateTime? = null

    companion object {

        fun of(entity: TrackEntity, phase: GalaxyPhaseEntity?): TrackVo {
            return TrackVo().apply {
                this.id = entity.id
                this.sourceId = entity.sourceId
                this.albumId = entity.albumId
                this.uid = entity.uid
                this.sessionCode = entity.sessionCode?.toString()
                this.phaseCode = entity.phaseCode?.toString()
                this.parentTitle = entity.parentTitle
                this.index = entity.trackIndex
                this.trackName = entity.trackName
                this.trackContent = entity.trackContent
                if (phase == null) {
                    this.trackStatus = "NOT_EXECUTED"
                } else {
                    if (PhaseState.FAILED.equalsCode(phase.phaseState)) {
                        this.trackStatus = "FAILED"
                    } else if (trackContent == null) {
                        this.trackStatus = "EXECUTING"
                    } else {
                        this.trackStatus = "EXECUTED"
                    }
                }
                this.createTime = entity.createTime
                this.updateTime = entity.updateTime
            }
        }

    }

}