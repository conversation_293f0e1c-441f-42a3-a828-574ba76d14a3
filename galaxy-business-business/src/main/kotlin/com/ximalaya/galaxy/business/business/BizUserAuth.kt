package com.ximalaya.galaxy.business.business

import com.ximalaya.galaxy.business.business.vo.*

/**
 * 用户认证业务接口
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
interface BizUserAuth {
    
    /**
     * 用户注册
     */
    fun register(request: RegisterRequestVo): Bo<PERSON>an
    
    /**
     * 用户登录
     */
    fun login(request: LoginRequestVo, ipAddress: String?, deviceInfo: String?): LoginResponseVo?
    
    /**
     * 用户登出
     */
    fun logout(token: String): Boolean
    
    /**
     * 刷新token
     */
    fun refreshToken(refreshToken: String): LoginResponseVo?
    
    /**
     * 获取用户信息
     */
    fun getUserInfo(userId: Long): UserInfoVo?
    
    /**
     * 生成邀请码
     */
    fun generateInvitationCode(count: Int, permissionId: Int, description: String?, creatorId: Long?): Boolean
    
    /**
     * 为用户生成个人邀请码
     */
    fun generatePersonalInvitationCode(userId: Long, permissionId: Int, description: String?): String?
    
    /**
     * 验证邀请码
     */
    fun validateInvitationCode(code: String): Boolean
    
    /**
     * 获取用户的邀请关系
     */
    fun getUserInvitationRelation(userId: Long): InvitationRelationVo?
} 