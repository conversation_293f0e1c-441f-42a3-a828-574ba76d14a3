package com.ximalaya.galaxy.business.business.vo

import com.ximalaya.galaxy.business.repo.entity.AlbumEntity
import com.ximalaya.galaxy.business.repo.entity.TrackEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.time.LocalDateTime
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotEmpty

/**
 *<AUTHOR>
 *@create 2025-06-02 16:50
 */
@ApiModel(description = "Agent专辑VO")
class AgentAlbumVo {

    @ApiModelProperty("sessionCode")
    @NotBlank(message = "sessionCode不能为空哦~")
    var sessionCode: String? = null

    @ApiModelProperty("专辑名称")
    @NotBlank(message = "专辑名称不能为空哦~")
    var theme: String? = null

    @ApiModelProperty("声音列表")
    @NotEmpty(message = "声音列表不能为空哦~")
    var tracks: List<AgentSaveTrackVo>? = null

    fun toSaveAlbumVo(): SaveAgentAlbumVo {
        val result = SaveAgentAlbumVo().apply {
            this.albumName = <EMAIL>
        }

        var index = 0
        val trackList = mutableListOf<SaveAgentTrackVo>()
        for (track in tracks!!) {
            val entity = SaveAgentTrackVo().apply {
                this.sourceId = track.id
                this.parentTitle = track.parentTitle
                this.index = index++
                this.trackName = track.title
            }
            trackList.add(entity)
        }

        result.tracks = trackList

        return result
    }

    companion object {

        fun of(album: AlbumEntity, tracks: Collection<TrackEntity>?): AgentAlbumVo {
            return AgentAlbumVo().apply {
                this.theme = album.albumName
                this.tracks = tracks?.sortedBy { it.trackIndex }?.let {
                    AgentSaveTrackVo.of(it)
                }
            }
        }

    }

}

@ApiModel(description = "Agent声音VO")
class AgentSaveTrackVo {

    @ApiModelProperty("id")
    @NotBlank(message = "id不能为空哦~")
    var id: String? = null

    @ApiModelProperty("parentTitle")
    @NotBlank(message = "parentTitle不能为空哦~")
    var parentTitle: String? = null

    @ApiModelProperty("title")
    @NotBlank(message = "title不能为空哦~")
    var title: String? = null

    companion object {

        fun of(tracks: Collection<TrackEntity>): List<AgentSaveTrackVo> {
            return tracks.map {
                AgentSaveTrackVo().apply {
                    this.id = it.sourceId
                    this.parentTitle = it.parentTitle
                    this.title = it.trackName
                }
            }
        }

    }

}

@ApiModel(description = "保存专辑VO")
class SaveAgentAlbumVo {

    @ApiModelProperty("专辑名称")
    var albumName: String? = null

    @ApiModelProperty("声音列表")
    var tracks: List<SaveAgentTrackVo>? = null

}

@ApiModel(description = "保存声音VO")
class SaveAgentTrackVo {

    @ApiModelProperty("sourceId")
    var sourceId: String? = null

    @ApiModelProperty("父标题")
    var parentTitle: String? = null

    @ApiModelProperty("声音索引")
    var index: Int? = null

    @ApiModelProperty("声音名称")
    var trackName: String? = null

    fun toEntity(albumId: Long, uid: Long, sessionCode: Long, date: LocalDateTime): TrackEntity {
        return TrackEntity().apply {
            this.albumId = albumId
            this.uid = uid
            this.sessionCode = sessionCode
            this.sourceId = <EMAIL>
            this.trackIndex = <EMAIL>
            this.parentTitle = <EMAIL>
            this.trackName = <EMAIL>
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = date
            this.updateTime = date
        }
    }

    fun diffTrackName(other: TrackEntity): Boolean {
        return trackName == other.trackName
    }

}