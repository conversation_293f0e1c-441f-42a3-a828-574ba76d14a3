package com.ximalaya.galaxy.business.business.vo

import com.ximalaya.hot.track.service.vo.DifyRequestVo
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

/**
 *<AUTHOR>
 *@create 2025-06-26 22:44
 */
@ApiModel(description = "直接调用dify VO")
class DirectBlockingCallDifyVo {

    @ApiModelProperty("输入")
    var inputs: Map<String, String?>? = null

    fun toDifyRequestVo(appSecret: String, uid: Long): DifyRequestVo {
        return DifyRequestVo().apply {
            this.appSecret = appSecret
            this.inputs = <EMAIL>
            this.user = uid.toString()
        }
    }

}