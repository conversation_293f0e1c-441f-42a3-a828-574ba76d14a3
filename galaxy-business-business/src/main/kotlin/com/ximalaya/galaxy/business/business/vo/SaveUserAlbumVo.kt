package com.ximalaya.galaxy.business.business.vo

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.apache.commons.lang3.StringUtils
import java.util.*
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotEmpty

/**
 *<AUTHOR>
 *@create 2025-06-26 18:49
 */
@ApiModel(description = "User专辑VO")
class UserAlbumVo {

    @ApiModelProperty("sessionCode")
    @NotBlank(message = "sessionCode不能为空哦~")
    var sessionCode: String? = null

    @ApiModelProperty("专辑名称")
    @NotBlank(message = "专辑名称不能为空哦~")
    var albumName: String? = null

    @ApiModelProperty("声音列表")
    @NotEmpty(message = "声音列表不能为空哦~")
    var tracks: List<UserSaveTrackVo>? = null

    fun toAgentAlbumVo(): AgentAlbumVo {
        return AgentAlbumVo().apply {
            this.sessionCode = <EMAIL>
            this.theme = <EMAIL>
            this.tracks = <EMAIL>?.map {
                it.toAgentTrackVo()
            }
        }
    }

}

@ApiModel(description = "User声音VO")
class UserSaveTrackVo {

    @ApiModelProperty("sourceId")
    var sourceId: String? = null

    @ApiModelProperty("parentTitle")
    @NotBlank(message = "parentTitle不能为空哦~")
    var parentTitle: String? = null

    @ApiModelProperty("title")
    @NotBlank(message = "title不能为空哦~")
    var trackName: String? = null

    fun toAgentTrackVo(): AgentSaveTrackVo {
        return AgentSaveTrackVo().apply {
            this.id = if (StringUtils.isNotBlank(<EMAIL>)) {
                <EMAIL>
            } else {
                UUID.randomUUID().toString().replace("-", "")
            }
            this.parentTitle = <EMAIL>
            this.title = <EMAIL>
        }
    }

}