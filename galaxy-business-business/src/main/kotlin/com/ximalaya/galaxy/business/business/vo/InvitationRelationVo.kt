package com.ximalaya.galaxy.business.business.vo

import java.time.LocalDateTime

/**
 * 邀请关系VO
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
data class InvitationRelationVo(
    val userId: Long,
    val username: String,
    val nickname: String?,
    val invitationCode: String?,
    val inviterId: Long?,
    val inviterUsername: String?,
    val inviterNickname: String?,
    val invitedUsers: List<InvitedUserVo> = emptyList(),
    val createTime: LocalDateTime?
)

/**
 * 被邀请用户VO
 * <AUTHOR>
 * @create 2025-01-15 10:00
 */
data class InvitedUserVo(
    val userId: Long,
    val username: String,
    val nickname: String?,
    val createTime: LocalDateTime?
) 