package com.ximalaya.galaxy.business.business.support;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Optional;

/**
 * 事务执行器
 *
 * <AUTHOR>
 * @create 2022-06-06 10:31
 */
@Slf4j
public class TransactionExecutor {

    private final TransactionTemplate transactionTemplate;

    private final int originalPropagationBehavior;

    private TransactionExecutor(TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
        this.originalPropagationBehavior = transactionTemplate.getPropagationBehavior();
    }

    public static TransactionExecutor create(TransactionTemplate transactionTemplate) {
        return new TransactionExecutor(transactionTemplate);
    }

    public <T> Optional<T> executeRequiresNew(TransactionCallback<T> action) {
        return execute(action, TransactionDefinition.PROPAGATION_REQUIRES_NEW);
    }

    public <T> T executeRequiresNewAndThrow(TransactionCallback<T> action) {
        return executeAndThrow(action, TransactionDefinition.PROPAGATION_REQUIRES_NEW);
    }

    public <T> Optional<T> execute(TransactionCallback<T> action, int propagationBehavior) {
        try {
            transactionTemplate.setPropagationBehavior(propagationBehavior);

            return Optional.ofNullable(transactionTemplate.execute(action));
        } catch (Exception e) {
            log.error("[TransactionExecutor] 执行出错将进行回滚", e);
        } finally {
            transactionTemplate.setPropagationBehavior(originalPropagationBehavior);
        }
        return Optional.empty();
    }

    public <T> T executeAndThrow(TransactionCallback<T> action, int propagationBehavior) {
        try {
            transactionTemplate.setPropagationBehavior(propagationBehavior);

            return transactionTemplate.execute(action);
        } catch (Exception e) {
            log.error("[TransactionExecutor] 执行出错将进行回滚", e);
            throw e;
        } finally {
            transactionTemplate.setPropagationBehavior(originalPropagationBehavior);
        }
    }

}
