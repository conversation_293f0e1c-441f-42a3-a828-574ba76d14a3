/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.boss.api.converter;

import java.nio.ByteBuffer;
import java.util.*;


public class PongResponseConverter {

  public static com.ximalaya.galaxy.business.boss.api.model.PongResponse transform(com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse source) {
    com.ximalaya.galaxy.business.boss.api.model.PongResponse target = new com.ximalaya.galaxy.business.boss.api.model.PongResponse();
    if (null != source) {
      if (source.isSetMessage()) {
        String _message_item = source.getMessage();
        target.setMessage(_message_item);
      }
    }
    return target;
  }

  public static com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse transform(com.ximalaya.galaxy.business.boss.api.model.PongResponse source) {
    com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse.Builder target = new com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse.Builder();
    if (null != source) {
      if (source.getMessage() != null) {
        String _message_item = source.getMessage();
        target.setMessage(_message_item);
      }
    }
    return target.build();
  }
}