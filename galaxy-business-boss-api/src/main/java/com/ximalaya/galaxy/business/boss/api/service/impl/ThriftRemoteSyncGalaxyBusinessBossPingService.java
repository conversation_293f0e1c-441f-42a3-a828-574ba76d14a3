/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.boss.api.service.impl;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.lang.RuntimeException;

import org.springframework.beans.factory.annotation.Autowired;

import com.ximalaya.mainstay.rpc.thrift.TException;

import com.ximalaya.galaxy.business.boss.api.service.*;
import com.ximalaya.galaxy.business.boss.api.ex.*;


public class ThriftRemoteSyncGalaxyBusinessBossPingService implements IRemoteSyncGalaxyBusinessBossPingService {

  @Autowired
  private com.ximalaya.galaxy.business.boss.api.thrift.GalaxyBusinessBossPingService.Iface client;

  
  @Override
  public com.ximalaya.galaxy.business.boss.api.model.PongResponse ping(com.ximalaya.galaxy.business.boss.api.model.PingRequest pingRequest) {
    try {
      com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest _pingRequest_arg_item = com.ximalaya.galaxy.business.boss.api.converter.PingRequestConverter.transform(pingRequest);
  
      com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse client_result = client.ping(_pingRequest_arg_item);
      if (client_result == null) {
        return null;
      }
  
      com.ximalaya.galaxy.business.boss.api.model.PongResponse _client_result_result_item = com.ximalaya.galaxy.business.boss.api.converter.PongResponseConverter.transform(client_result);
      return _client_result_result_item;
    } catch (TException e) {
      throw new GalaxyBusinessBossPingServiceException("call ping failed.", e);
    }
  }
}