/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.boss.api.thrift;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ximalaya.mainstay.common.MainstayCodecException;
import com.ximalaya.mainstay.common.Option;
import com.ximalaya.mainstay.common.RouteArg;
import com.ximalaya.mainstay.common.concurrent.FutureCallback;
import com.ximalaya.mainstay.common.concurrent.Futures;
import com.ximalaya.mainstay.common.concurrent.Future;
import com.ximalaya.mainstay.common.concurrent.SettableFuture;
import com.ximalaya.mainstay.common.util.Utilities;
import com.ximalaya.mainstay.rpc.thrift.processor.TBaseProcessor;
import com.ximalaya.mainstay.rpc.thrift.processor.TProcessFunction;
import com.ximalaya.mainstay.rpc.thrift.processor.TProcessor;
import com.ximalaya.mainstay.rpc.thrift.TCodec;
import com.ximalaya.mainstay.rpc.thrift.TFutureClient;
import com.ximalaya.mainstay.rpc.thrift.ThriftMessage;
import com.ximalaya.mainstay.rpc.thrift.ThriftStruct;
import com.ximalaya.mainstay.rpc.thrift.ThriftStructCodec;
import com.ximalaya.mainstay.rpc.thrift.TApplicationException;
import com.ximalaya.mainstay.rpc.thrift.TException;
import com.ximalaya.mainstay.rpc.thrift.protocol.TField;
import com.ximalaya.mainstay.rpc.thrift.protocol.TList;
import com.ximalaya.mainstay.rpc.thrift.protocol.TMap;
import com.ximalaya.mainstay.rpc.thrift.protocol.TSet;
import com.ximalaya.mainstay.rpc.thrift.protocol.TProtocol;
import com.ximalaya.mainstay.rpc.thrift.protocol.TProtocolException;
import com.ximalaya.mainstay.rpc.thrift.protocol.TProtocolFactory;
import com.ximalaya.mainstay.rpc.thrift.protocol.TProtocolUtil;
import com.ximalaya.mainstay.rpc.thrift.protocol.TStruct;
import com.ximalaya.mainstay.rpc.thrift.protocol.TType;
import com.ximalaya.mainstay.transport.Client;
import com.ximalaya.mainstay.transport.Message;


public class GalaxyBusinessBossPingService {
  public static interface Iface {
    
    public com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse ping(com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest pingRequest) throws TException;
  }

  public static interface FutureIface {
    
    public Future<com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse> ping(com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest pingRequest);
  }
  
  public static class Ping {
  public static class Args implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("ping_args");
      private static final TField PingRequestField = new TField("pingRequest", TType.STRUCT, (short) 1);
      final com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest pingRequest;
      boolean isSetPingRequest;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest _pingRequest = null;
        private boolean isSetPingRequest = false;
        public Builder setPingRequest(com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest value) {
          this._pingRequest = value;
          if (value != null) {
            this.isSetPingRequest = true;
          }
          return this;
        }
    
    
        public boolean isSetPingRequest() {
          return isSetPingRequest;
        }
    
        public com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest getPingRequest() {
          return this._pingRequest;
        }
    
        public Builder unsetPingRequest() {
          this._pingRequest = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptyPingRequest() {
          this._pingRequest = null;
          this.isSetPingRequest = true;
          return this;
        }
    
    
        public Args build() {
          Args model = new Args(this._pingRequest);
          model.isSetPingRequest = isSetPingRequest;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (isSetPingRequest) builder.setPingRequest(this.pingRequest);
        return builder;
      }
    
      public static final ThriftStructCodec<Args> CODEC = new ThriftStructCodec<Args>() {
        @Override
        public Args decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest pingRequest = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 1: /* pingRequest */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest pingRequest_item;
                      pingRequest_item = com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest.decode(_iprot);
                      pingRequest = pingRequest_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setPingRequest(pingRequest);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Args struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Args decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Args struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Args(com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest pingRequest) {
        this.pingRequest = pingRequest;
      }
    
    
      public com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest getPingRequest() {
        return this.pingRequest;
      }
  
      public boolean isSetPingRequest() {
        return this.isSetPingRequest;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetPingRequest) {
          if (pingRequest != null) {
            _oprot.writeFieldBegin(PingRequestField);
            com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest pingRequest_item = pingRequest;
            pingRequest_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Args)) {
          return false;
        }
        Args that = (Args) other;
        if (this.pingRequest == null) {
          if (that.pingRequest != null)
            return false;
        } else if (!this.pingRequest.equals(that.pingRequest)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Args(");
        if (this.isSetPingRequest) {
          sb.append("pingRequest=");
          sb.append(this.pingRequest);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.pingRequest == null ? 0 : this.pingRequest.hashCode());
        return hash;
      }
    }
  public static class Result implements ThriftStruct {
      private static final TStruct STRUCT = new TStruct("ping_result");
      private static final TField SuccessField = new TField("success", TType.STRUCT, (short) 0);
      final Option<com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse> success;
      boolean isSetSuccess;
    
      public static class Builder {
        private com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse _success = null;
        private boolean isSetSuccess = false;
        public Builder setSuccess(com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse value) {
          this._success = value;
          if (value != null) {
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public Builder setSuccessOpt(Option<com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse> value) {
          if (value.isDefined()) {
            this._success = value.get();
            this.isSetSuccess = true;
          }
          return this;
        }
    
        public boolean isSetSuccess() {
          return isSetSuccess;
        }
    
        public com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse getSuccess() {
          return this._success;
        }
    
        public Builder unsetSuccess() {
          this._success = null;
          return this;
        }
    
        // 仅用于发送空字段到服务端
        public Builder emptySuccess() {
          this._success = null;
          this.isSetSuccess = true;
          return this;
        }
    
    
        public Result build() {
          Result model = new Result(Option.fromNullable(this._success) );
          model.isSetSuccess = isSetSuccess;
          return model;
        }
      }
    
      public Builder copy() {
        Builder builder = new Builder();
        if (this.success.isDefined() && isSetSuccess) builder.setSuccess(this.success.get());
        return builder;
      }
    
      public static final ThriftStructCodec<Result> CODEC = new ThriftStructCodec<Result>() {
        @Override
        public Result decode(TProtocol _iprot) throws TException {
          Builder builder = new Builder();
          com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse success = null;
          Boolean _done = false;
          _iprot.readStructBegin();
          while (!_done) {
            TField _field = _iprot.readFieldBegin();
            if (_field.type == TType.STOP) {
              _done = true;
            } else {
              switch (_field.id) {
                case 0: /* success */
                  switch (_field.type) {
                    case TType.STRUCT:
                      com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse success_item;
                      success_item = com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse.decode(_iprot);
                      success = success_item;
                      break;
                    default:
                      TProtocolUtil.skip(_iprot, _field.type);
                  }
                  builder.setSuccess(success);
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              _iprot.readFieldEnd();
            }
          }
          _iprot.readStructEnd();
          try {
            return builder.build();
          } catch (IllegalStateException stateEx) {
            throw new TProtocolException(stateEx.getMessage());
          }
        }
    
        @Override
        public void encode(Result struct, TProtocol oprot) throws TException {
          struct.write(oprot);
        }
      };
    
      public static Result decode(TProtocol _iprot) throws TException {
        return CODEC.decode(_iprot);
      }
    
      public static void encode(Result struct, TProtocol oprot) throws TException {
        CODEC.encode(struct, oprot);
      }
    
      Result(Option<com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse> success) {
        this.success = success;
      }
    
    
      public com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse getSuccess() {
        return this.success.get();
      }
        public Option<com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse> getSuccessOpt() {
        return this.success;
      }
  
      public boolean isSetSuccess() {
        return this.isSetSuccess;
      }
    
      @Override
      public void write(TProtocol _oprot) throws TException {
        validate();
        _oprot.writeStructBegin(STRUCT);
        if (this.isSetSuccess) {
          if (success.isDefined()) {
            _oprot.writeFieldBegin(SuccessField);
            com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse success_item = success.get();
            success_item.write(_oprot);
            _oprot.writeFieldEnd();
          }
        }
        _oprot.writeFieldStop();
        _oprot.writeStructEnd();
      }
    
      private void validate() throws TProtocolException {
      }
    
    
      @Override
      public boolean equals(Object other) {
        if (!(other instanceof Result)) {
          return false;
        }
        Result that = (Result) other;
        if (this.success == null) {
          if (that.success != null)
            return false;
        } else if (!this.success.equals(that.success)) {
          return false;
        }
        return true;
      }
    
      private int hash(Object... values) {
        return java.util.Arrays.hashCode(values);
      }
    
      @Override
      public String toString() {
        StringBuilder sb = new StringBuilder("Result(");
        if (this.isSetSuccess) {
          sb.append("success=");
          sb.append(this.success);
          sb.append(", ");
        }
        sb.append(")");
        return sb.toString();
      }
    
      @Override
      public int hashCode() {
        int hash = 1;
        hash = 31 * hash + hash * (this.success.isDefined() ? this.success.get().hashCode() : 0);
        return hash;
      }
    }
  }


  public static class Processor extends TBaseProcessor<Iface> implements TProcessor {
    private static final Logger LOG = LoggerFactory.getLogger(Processor.class);
    public Processor(Iface iface) {
      super(iface, getProcessMap(new HashMap<String, TProcessFunction<Iface, ? extends ThriftStruct>>()));
    }
  
    public Processor(Iface iface, Map<String, TProcessFunction<Iface, ? extends ThriftStruct>> processMap) {
      super(iface, getProcessMap(processMap));
    }
    
    private static <I extends Iface> Map<String,  TProcessFunction<Iface, ? extends ThriftStruct>> getProcessMap(Map<String, TProcessFunction<Iface, ? extends ThriftStruct>> processMap) {
      processMap.put("ping", new PingFunction());
      return processMap;
    }
    private static class PingFunction extends TProcessFunction<Iface, Ping.Args> {
      public PingFunction() {
        super("ping");
      }
    
      @Override
      protected boolean isOneway() {
        return false;
      }
    
      @Override
      public ThriftStruct getResult(Iface iface, Ping.Args args) throws TException {
        Ping.Result.Builder builder = new Ping.Result.Builder();
        com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse result = iface.ping(args.pingRequest);
        builder.setSuccess(result);
        return builder.build();
      }
    }
  }
  
  
  public static class FutureClient extends TFutureClient implements FutureIface {
    private static final Logger LOG = LoggerFactory.getLogger(FutureClient.class);

    public FutureClient(Client client) {
      super(client);
    }
    
    @Override
    public Future<com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse> ping(com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest pingRequest) {
      Ping.Args.Builder builder = new Ping.Args.Builder();
      builder.setPingRequest(pingRequest);
      Ping.Args args = builder.build();
      ThriftMessage request = encodeRequest("ping", args);
      if (LOG.isDebugEnabled()) {
        LOG.debug("send thrift message {}",request);
      }
      Future<Message> responseFuture = client.send(request);
      final SettableFuture<com.ximalaya.galaxy.business.boss.api.thrift.entity.PongResponse> settableFuture = Futures.newSettableFuture(request);
      Futures.addCallback(responseFuture, new FutureCallback<Message>() {
    
        @Override
        public void onSuccess(Message response) {
          Ping.Result result = null;
          try {
            ThriftMessage thriftMessage = (ThriftMessage) response;
            if (LOG.isDebugEnabled()) {
              LOG.debug("receive thrift message {}", thriftMessage);
            }
            result = decodeResponse(thriftMessage);
          } catch (Throwable e) {
            settableFuture.setException(e);
            return;
          }
          if (result.isSetSuccess()) {
            settableFuture.set(result.getSuccess());
            return;
          }
          settableFuture.set(null);
        }
    
        @Override
        public void onFailure(Throwable t) {
          settableFuture.setException(t);
        }
      });
      return settableFuture;
    }
  }
  
  public static class ArgCodec extends TCodec {
    
    public ArgCodec(){
      super(getArgsCodecMap(new HashMap<String, ThriftStructCodec>()));
    }
    
    public static final Map<String, ThriftStructCodec> getArgsCodecMap(Map<String, ThriftStructCodec> codecMap) {
      codecMap.put("ping", Ping.Args.CODEC);
      return codecMap;
    }
  }
  public static class ResultCodec extends TCodec {
    
    public ResultCodec(){
      super(getResultCodecMap(new HashMap<String, ThriftStructCodec>()));
    }
    
    public static final Map<String, ThriftStructCodec> getResultCodecMap(Map<String, ThriftStructCodec> codecMap) {
      codecMap.put("ping", Ping.Result.CODEC);
      return codecMap;
    }
  }
}