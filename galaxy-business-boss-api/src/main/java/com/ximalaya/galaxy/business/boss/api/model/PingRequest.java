/**
 * Generated by Dal<PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.boss.api.model;

import java.nio.ByteBuffer;
import java.util.*;


public class PingRequest {

  private String message;

  public PingRequest() {
  }

  public PingRequest(String message) {
    this.message = message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getMessage() {
    return this.message;
  }

  @Override
  public String toString() {
    return "PingRequest(" +"message=" + this.message + ")";
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + (this.message == null ? 0 : this.message.hashCode());
    return hash;
  }

  @Override
  public boolean equals(Object other) {
    if (!(other instanceof PingRequest)) {
      return false;
    }
    PingRequest that = (PingRequest) other;
    if (this.message == null) {
      if (that.message != null)
        return false;
    } else if (!this.message.equals(that.message)) {
      return false;
    }
    return true;
  }
}