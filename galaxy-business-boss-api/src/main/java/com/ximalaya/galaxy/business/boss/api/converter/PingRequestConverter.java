/**
 * Generated by Dal<PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.boss.api.converter;

import java.nio.ByteBuffer;
import java.util.*;


public class PingRequestConverter {

  public static com.ximalaya.galaxy.business.boss.api.model.PingRequest transform(com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest source) {
    com.ximalaya.galaxy.business.boss.api.model.PingRequest target = new com.ximalaya.galaxy.business.boss.api.model.PingRequest();
    if (null != source) {
      if (source.isSetMessage()) {
        String _message_item = source.getMessage();
        target.setMessage(_message_item);
      }
    }
    return target;
  }

  public static com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest transform(com.ximalaya.galaxy.business.boss.api.model.PingRequest source) {
    com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest.Builder target = new com.ximalaya.galaxy.business.boss.api.thrift.entity.PingRequest.Builder();
    if (null != source) {
      if (source.getMessage() != null) {
        String _message_item = source.getMessage();
        target.setMessage(_message_item);
      }
    }
    return target.build();
  }
}