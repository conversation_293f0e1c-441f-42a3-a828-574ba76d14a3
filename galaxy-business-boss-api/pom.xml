<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ximalaya</groupId>
    <artifactId>galaxy-business</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>galaxy-business-boss-api</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>jar</packaging>

  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.ximalaya.eros</groupId>
      <artifactId>eros-starter-mainstay</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <compilerArgs>
            <arg>-parameters</arg>
          </compilerArgs>
          <encoding>${project.build.sourceEncoding}</encoding>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>2.10</version>
      </plugin>
      <plugin>
        <groupId>com.ximalaya.dalaran</groupId>
        <artifactId>dalaran-maven-plugin</artifactId>
        <version>0.0.61</version>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <language>java</language>
          <outputDirectory>src/main/java</outputDirectory>
          <includePattern>**/thrift/*.thrift</includePattern>
          <excludePattern></excludePattern>
          <thriftOpts>
            <!-- 表示启用生成整个 business-api 的代码 -->
            <thriftOpt>-J</thriftOpt>
            <!-- 表示 仅做语法检查，不生成代码文件 (这个存在就不会生成) -->
            <thriftOpt>-D</thriftOpt>
          </thriftOpts>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <distributionManagement>
    <repository>
      <id>artifactory</id>
      <name>ximalaya-releases</name>
      <url>http://artifactory.ximalaya.com/artifactory/ximalaya-releases/</url>
    </repository>
    <snapshotRepository>
      <id>artifactory</id>
      <name>ximalaya-snapshots</name>
      <url>http://artifactory.ximalaya.com/artifactory/ximalaya-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

</project>