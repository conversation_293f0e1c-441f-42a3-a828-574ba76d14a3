<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ximalaya</groupId>
    <artifactId>galaxy-business</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>galaxy-business-service</artifactId>
  <version>0.0.1-SNAPSHOT</version>

  <dependencies>
    <dependency>
      <groupId>com.ximalaya</groupId>
      <artifactId>galaxy-business-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ximalaya</groupId>
      <artifactId>galaxy-business-repo</artifactId>
    </dependency>

    <!-- 内容安全管控 -->
<!--    <dependency>-->
<!--      <groupId>com.ximalaya.audit.security</groupId>-->
<!--      <artifactId>audit-security-macfarlane-api</artifactId>-->
<!--    </dependency>-->

    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy</artifactId>
    </dependency>

    <dependency>
      <groupId>org.craftercms</groupId>
      <artifactId>groovy-sandbox</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
    </dependency>

    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>1.5.13</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>com.ximalaya.eros</groupId>-->
<!--      <artifactId>eros-starter-danube</artifactId>-->
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <artifactId>xdcs-log4j</artifactId>-->
<!--          <groupId>com.ximalaya.xdcs</groupId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <groupId>com.ximalaya.danube</groupId>-->
<!--          <artifactId>danube-core</artifactId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <artifactId>football-config-client</artifactId>-->
<!--          <groupId>com.ximalaya.football</groupId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>com.ximalaya.danube</groupId>-->
<!--      <artifactId>danube-core</artifactId>-->
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <groupId>com.ximalaya.football</groupId>-->
<!--          <artifactId>football-config-client</artifactId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <groupId>com.ximalaya.football</groupId>-->
<!--          <artifactId>football-common</artifactId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
<!--    </dependency>-->
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-maven-plugin</artifactId>
        <version>${kotlin.version}</version>
        <configuration>
          <jvmTarget>1.8</jvmTarget>
          <args>
            <arg>-Xjsr305=strict</arg>
          </args>
          <compilerPlugins>
            <plugin>spring</plugin>
          </compilerPlugins>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-maven-allopen</artifactId>
            <version>${kotlin.version}</version>
          </dependency>
        </dependencies>
        <executions>
          <execution>
            <id>compile</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                <sourceDir>${project.basedir}/src/main/java</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
          <execution>
            <id>test-compile</id>
            <goals>
              <goal>test-compile</goal>
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                <sourceDir>${project.basedir}/src/test/java</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.5.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
        <executions>
          <!-- 替换会被 maven 特别处理的 default-compile -->
          <execution>
            <id>default-compile</id>
            <phase>none</phase>
          </execution>
          <!-- 替换会被 maven 特别处理的 default-testCompile -->
          <execution>
            <id>default-testCompile</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>java-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
          <execution>
            <id>java-test-compile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>