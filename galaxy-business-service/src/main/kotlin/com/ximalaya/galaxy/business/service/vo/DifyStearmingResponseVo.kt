package com.ximalaya.hot.track.service.vo

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.alibaba.fastjson.annotation.JSONField
import com.alibaba.fastjson.parser.DefaultJSONParser
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer
import com.ximalaya.galaxy.business.common.enums.Enums
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import org.apache.commons.lang3.StringUtils
import java.lang.reflect.Type


/**
 *<AUTHOR>
 *@create 2024-12-04 14:00
 */
class DifyStreamingResponseVo<T> {

  @JSONField(deserializeUsing = DifyEventDeserializer::class)
  var event: DifyEvent? = null

  @JSONField(name = "task_id")
  var taskId: String? = null

  @JSONField(name = "workflow_run_id")
  var workflowRunId: String? = null

  var data: T? = null

  fun isSuccess(): Boolean {
    return when (event) {
      DifyEvent.STARTED -> true
      DifyEvent.NODE_STARTED -> true
      DifyEvent.NODE_FINISHED -> if (data == null) return false else (data as DifyStreamingNodeFinishedDataVo).status == "succeeded"
      DifyEvent.FINISHED -> true
      DifyEvent.GENERATING -> true
      else -> true
    }
  }

  companion object {

    fun parseJson(json: String): DifyStreamingResponseVo<*> {
      val jsonObject = JSON.parseObject(json)
      val eventCode = jsonObject.getString("event")
      if (StringUtils.isBlank(eventCode)) {
        throw GalaxyException(ErrorCode.CALL_DIFY_ERROR, "Event为空")
      }

      val event = DifyEvent.parseCode(eventCode)

      return when (event) {
        DifyEvent.STARTED -> JSON.parseObject(json, object : TypeReference<DifyStreamingResponseVo<DifyStreamingStartedDataVo>>() {})
        DifyEvent.NODE_STARTED -> JSON.parseObject(json, object : TypeReference<DifyStreamingResponseVo<DifyStreamingNodeStartedDataVo>>() {})
        DifyEvent.NODE_FINISHED -> JSON.parseObject(json, object : TypeReference<DifyStreamingResponseVo<DifyStreamingNodeFinishedDataVo>>() {})
        DifyEvent.FINISHED -> JSON.parseObject(json, object : TypeReference<DifyStreamingResponseVo<DifyStreamingFinishedDataVo>>() {})
        DifyEvent.GENERATING -> JSON.parseObject(json, object : TypeReference<DifyStreamingResponseVo<DifyStreamingGeneratingDataVo>>() {})
        else -> throw GalaxyException(ErrorCode.CALL_DIFY_ERROR, "解析结果失败")
      }
    }

  }

  override fun toString() =
    "DifyStreamingResponseVo(${JSON.toJSONString(this)})"

}

class DifyStreamingStartedDataVo {

  var id: String? = null

  @JSONField(name = "workflow_id")
  var workflowId: String? = null

  @JSONField(name = "sequence_number")
  var sequenceNumber: Long? = null

  var inputs: Map<String, String>? = null

  @JSONField(name = "created_at")
  var createAt: Long? = null

  override fun toString() =
    "DifyStreamingStartedDataVo(${JSON.toJSONString(this)})"

}

class DifyStreamingNodeStartedDataVo {

  var id: String? = null

  @JSONField(name = "title")
  var title: String? = null

  var index: Int? = null

  @JSONField(name = "predecessor_node_id")
  var predecessorNodeId: String? = null

  var inputs: Map<String, String>? = null

  @JSONField(name = "created_at")
  var createdAt: Long? = null

  override fun toString() =
    "DifyStreamingNodeStartedDataVo(${JSON.toJSONString(this)})"
}

class DifyStreamingNodeFinishedDataVo {

  var id: String? = null

  @JSONField(name = "title")
  var title: String? = null

  var index: Int? = null

  @JSONField(name = "predecessor_node_id")
  var predecessorNodeId: String? = null

  var inputs: Map<String, String>? = null

  var outputs: Map<String, String>? = null

  var status: String? = null

  var error: String? = null

  @JSONField(name = "created_at")
  var createdAt: Long? = null

  override fun toString() =
    "DifyStreamingNodeFinishedDataVo(${JSON.toJSONString(this)})"
}

class DifyStreamingFinishedDataVo {

  var id: String? = null

  @JSONField(name = "workflow_id")
  var workflowId: String? = null

  var status: String? = null

  var outputs: Map<String, String>? = null

  var error: String? = null

  @JSONField(name = "elapsed_time")
  var elapsedTime: Double? = null

  @JSONField(name = "total_tokens")
  var totalTokens: Int? = null

  @JSONField(name = "total_steps")
  var totalSteps: Int? = null

  @JSONField(name = "created_by")
  var createdBy: Map<String, String>? = null

  @JSONField(name = "created_at")
  var createdAt: Long? = null

  @JSONField(name = "finished_at")
  var finishedAt: Long? = null

  override fun toString() =
    "DifyStreamingFinishedDataVo(${JSON.toJSONString(this)})"

}

class DifyStreamingGeneratingDataVo {

  var text: String? = null

}

// 自定义反序列化器
class DifyEventDeserializer : ObjectDeserializer {

  override fun <T : Any?> deserialze(parser: DefaultJSONParser?, type: Type?, fieldName: Any?): T {
    val code = parser?.parseObject(String::class.java)
    return DifyEvent.parseCode(code) as T
  }

  override fun getFastMatchToken(): Int {
    return 0
  }

}

enum class DifyEvent(val code: String) {

  /**
   * 流程启动
   */
  STARTED("workflow_started"),

  /**
   * 节点开始
   */
  NODE_STARTED("node_started"),

  /**
   * 节点结束
   */
  NODE_FINISHED("node_finished"),

  /**
   * 流程结束
   */
  FINISHED("workflow_finished"),

  /**
   * 生成中
   */
  GENERATING("text_chunk"),

  /**
   * 心跳
   */
  PING("ping"),
  ;

  companion object {

    fun parseCode(code: String?): DifyEvent {
      return Enums.parseNotNull(
        DifyEvent.values(),
        DifyEvent::code,
        code,
        "Enum not support DifyEvent: $code"
      )
    }

  }

}