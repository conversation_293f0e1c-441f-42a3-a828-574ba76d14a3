package com.ximalaya.galaxy.business.service

import com.ximalaya.galaxy.business.service.vo.AgentBlockChatRequestVo
import com.ximalaya.galaxy.business.service.vo.AgentBlockResponseVo
import com.ximalaya.galaxy.business.service.vo.AgentChatRequestVo
import okhttp3.Callback

/**
 *<AUTHOR>
 *@create 2025-05-21 11:12
 */
interface AgentService {

  fun streamCall(vo: AgentChatRequestVo, callback: Callback)

  fun blockCall(vo: AgentBlockChatRequestVo): AgentBlockResponseVo

}