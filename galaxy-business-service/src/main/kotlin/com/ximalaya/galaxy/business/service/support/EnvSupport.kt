package com.ximalaya.galaxy.business.service.support

import com.ximalaya.galaxy.business.common.enums.Enums
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2024-12-10 22:27
 */
@Component
class EnvSupport {

    @Value("\${spring.profiles.active}")
    fun setEnv(activeProfile: String?) {
        env = Enums.parseNotNull(
            Env.values(),
            Env::profile,
            activeProfile,
            String.format("spring.profiles.active:%s 无法匹配Env", activeProfile)
        )
    }

    companion object {
        private var env: Env? = null

        fun currentEnv() = GalaxyAsserts.requireNonNull(env, ErrorCode.CONTENT_NOT_FOUND, "Env Not Found!!!")

        fun isDev() = Env.DEV == env

        fun online(): <PERSON><PERSON><PERSON> {
            return Env.PRODUCT == env
        }
    }

}