package com.ximalaya.galaxy.business.service.vo

import com.alibaba.fastjson.JSON

/**
 *<AUTHOR>
 *@create 2025-07-14 15:56
 */
class AgentBlockResponseVo {

    var text: String? = null

    var usage: AgentBlockUsageVo? = null

    companion object {

        fun parseJson(json: String): AgentBlockResponseVo = JSON.parseObject(json, AgentBlockResponseVo::class.java)

    }
}

class AgentBlockUsageVo {

    var promptTokens: Int? = null

    var completionTokens: Int? = null

    var totalTokens: Int? = null

}