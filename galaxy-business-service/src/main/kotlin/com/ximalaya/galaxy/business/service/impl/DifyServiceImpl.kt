package com.ximalaya.galaxy.business.service.impl

import com.ximalaya.galaxy.business.common.APPLICATION_JSON
import com.ximalaya.galaxy.business.common.AUTHORIZATION
import com.ximalaya.galaxy.business.common.CONTENT_TYPE
import com.ximalaya.galaxy.business.service.DifyService
import com.ximalaya.galaxy.business.service.configuration.props.GalaxyProperties
import com.ximalaya.galaxy.business.service.support.HttpSupport
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo
import okhttp3.Callback
import okhttp3.Headers
import okhttp3.OkHttpClient
import okhttp3.Request
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

/**
 *<AUTHOR>
 *@create 2024-12-04 13:29
 */
@Service
class DifyServiceImpl(
    private val galaxyProperties: GalaxyProperties,
    @Qualifier("difyHttpClient") private val httpClient: OkHttpClient,
) : DifyService {

    override fun blockingCall(vo: DifyRequestVo): DifyResponseVo {
        val headers = Headers.Builder()
            .add(AUTHORIZATION, "Bearer ${vo.appSecret}")
            .add(CONTENT_TYPE, APPLICATION_JSON)
            .build()

        val httpRequest = Request.Builder()
            .url(galaxyProperties.dify!!.path)
            .headers(headers)
            .post(vo.toRequestBody("blocking"))
            .build()

        val result = HttpSupport.syncCallDify(httpClient, httpRequest) { DifyResponseVo.parseJson(it) }
        return result
    }

    override fun streamingCall(vo: DifyRequestVo, callback: Callback) {
        val headers = Headers.Builder()
            .add(AUTHORIZATION, "Bearer ${vo.appSecret}")
            .add(CONTENT_TYPE, APPLICATION_JSON)
            .build()

        val httpRequest = Request.Builder()
            .url(galaxyProperties.dify!!.path)
            .headers(headers)
            .post(vo.toRequestBody("streaming"))
            .build()

        httpClient.newCall(httpRequest).enqueue(callback)
    }

}