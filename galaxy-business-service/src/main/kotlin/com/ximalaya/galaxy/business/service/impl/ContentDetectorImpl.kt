package com.ximalaya.galaxy.business.service.impl

import com.ximalaya.galaxy.business.service.ContentDetector
import mu.KotlinLogging
import org.springframework.stereotype.Service

/**
 *   <AUTHOR>
 *   @date 2025年05月27日 17:19
 *   @version ContentDetectorImpl.java
 */
@Service("contentDetector")
class ContentDetectorImpl(
//    private val contentInspectApi: IRemoteSyncContentInspectApi
) : ContentDetector {

    override fun detect(text: String): <PERSON><PERSON><PERSON> {
        return true
    }

//    override fun detectDetail(text: String): Pair<TextInsepectRequest, InsepectResult> {
//        val request = TextInsepectRequest()
//        request.business = "pgc"
//        request.channel = "ai_book"
//        request.text = text
//
//        // fixme: 去掉审核
//        return Pair(request, InsepectResult().apply {
//            this.result = 0
//            this.message = "内容检测接口调用异常，临时降级处理，默认通过"
//        })
//
//        var result: InsepectResult? = null
//        try {
//            result = contentInspectApi.inspectText(request)
//            return Pair(request, result)
//        } catch (e: Exception) {
//            logger.error("内容检测接口调用异常，默认通过", e)
//            result = InsepectResult().apply {
//                this.result = 0
//                this.message = "内容检测接口调用异常，临时降级处理，默认通过"
//            }
//            return Pair(request, result)
//        } finally {
//            logger.debug("内容检测 [{}], 结果: {}", text, result?.code == 0)
//        }
//    }

    companion object {
        private val logger = KotlinLogging.logger {}
    }
}