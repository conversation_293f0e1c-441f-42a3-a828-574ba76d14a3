package com.ximalaya.hot.track.service.vo

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.annotation.JSONField
import com.ximalaya.galaxy.business.service.support.EnvSupport
import okhttp3.RequestBody

/**
 *<AUTHOR>
 *@create 2024-12-04 13:26
 */
class DifyRequestVo {

    var appSecret: String? = null

    var inputs: Map<String, String?>? = null

    var user: String? = null

    fun toRequestBody(mode: String): RequestBody {
        val params = mapOf<String, Any>(
            "inputs" to (inputs ?: emptyMap()),
            "response_mode" to mode,
            "user" to (user ?: "${EnvSupport.currentEnv().profile}-user")
        )

        return RequestBody.create(
            null,
            JSON.toJSONString(params)
        )
    }

    @JSONField(serialize = false, deserialize = false)
    fun toJson(): String {
        return JSON.toJSONString(this)
    }

    companion object {

        fun parseJson(json: String): DifyRequestVo {
            return JSON.parseObject(json, DifyRequestVo::class.java)
        }

    }

}