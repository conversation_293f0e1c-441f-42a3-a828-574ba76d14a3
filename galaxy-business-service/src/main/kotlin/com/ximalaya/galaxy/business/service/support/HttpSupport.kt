package com.ximalaya.galaxy.business.service.support

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import okhttp3.OkHttpClient
import okhttp3.Request
import java.nio.charset.StandardCharsets

/**
 *<AUTHOR>
 *@create 2023-08-27 23:44
 */
object HttpSupport {

  fun <R> syncCallDify(
    httpClient: OkHttpClient,
    request: Request,
    parseData: (String) -> R
  ): R {
    val response = syncCall(httpClient, request, ErrorCode.CALL_DIFY_ERROR)
    return parseData(response)
  }

  fun <R> syncCallAgent(
    httpClient: OkHttpClient,
    request: Request,
    parseData: (String) -> R
  ): R {
    val response = syncCall(httpClient, request, ErrorCode.CALL_AGENT_ERROR)
    return parseData(response)
  }

  fun syncCall(
    httpClient: OkHttpClient,
    request: Request,
    callErrorCode: ErrorCode,
  ): String {
    return httpClient.newCall(request).execute()
      .use { response ->
        GalaxyAsserts.assertTrue(response.isSuccessful, callErrorCode)
        val responseBody = GalaxyAsserts.requireNonNull(
          response.body,
          ErrorCode.CONTENT_NOT_FOUND
        )
        val source = GalaxyAsserts.requireNonNull(
          responseBody.source(),
          ErrorCode.CONTENT_NOT_FOUND
        )

        source.readString(StandardCharsets.UTF_8)
      }
  }

}