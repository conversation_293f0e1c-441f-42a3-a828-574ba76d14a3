package com.ximalaya.galaxy.business.service.support


/**
 *<AUTHOR>
 *@create 2024-12-09 15:56
 */

const val BASE_CACHE_KEY = "galaxy:cache"

const val USER_INVITATION_CACHE_KEY = "$BASE_CACHE_KEY:user:invitation"

const val AGENT_MESSAGE_CACHE_KEY = "$BASE_CACHE_KEY:message"

const val AGENT_PENDING_MESSAGE_CACHE_KEY = "$BASE_CACHE_KEY:pending-message"

const val AGENT_STOP_KEY = "galaxy:stop-agent"

fun getUserInvitationCacheKey(uid: Long) = "$USER_INVITATION_CACHE_KEY:$uid"

fun getAgentMessageCacheKey(sessionCode: Long, phaseCode: Long) =
    "$AGENT_MESSAGE_CACHE_KEY:session:$sessionCode:phase:$phaseCode"

fun getAgentPendingMessageCacheKey(sessionCode: Long, phaseCode: Long) =
    "$AGENT_PENDING_MESSAGE_CACHE_KEY:session:$sessionCode:phase:$phaseCode"

fun getAgentStopKey(sessionCode: Long, phaseCode: Long) =
    "$AGENT_STOP_KEY:session:$sessionCode:phase:$phaseCode"

interface CacheLoader<T> {

    fun load(): T?

    fun serializer(instance: T): String

    fun deserializer(str: String): T

}