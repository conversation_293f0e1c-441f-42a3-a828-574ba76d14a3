package com.ximalaya.galaxy.business.service.vo

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.annotation.JSONField
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo

/**
 *<AUTHOR>
 *@create 2025-05-21 15:02
 */
abstract class AgentContentProtocol(
    var _type: AgentContentType
) {

    var _sessionCode: Long? = null
        set(value) {
            field = value
            sessionCode = value?.toString()
        }

    var sessionCode: String? = null

    var _phaseCode: Long? = null
        set(value) {
            field = value
            phaseCode = value?.toString()
        }

    var phaseCode: String? = null

    var index: Long? = null

    var _blockCode: Long? = null
        set(value) {
            field = value
            blockCode = value?.toString()
        }

    var blockCode: String? = null

    @JSONField(serialize = false, deserialize = false)
    fun toJson(): String {
        return JSON.toJSONString(this)
    }

    @JSONField(serialize = false, deserialize = false)
    fun toPair(): Pair<Long, Long> {
        return Pair(_sessionCode!!, _phaseCode!!)
    }

    override fun toString(): String {
        return this.javaClass.simpleName + "(${JSON.toJSONString(this)})"
    }

    companion object {

        @JvmStatic
        fun parseJson(json: String): AgentContentProtocol {
            val type = JSON.parseObject(json).getString("type")
            return when (AgentContentType.parseCode(type)) {
                AgentContentType.TEXT -> AgentContentText.parseJson(json)
                AgentContentType.TEXT_DELTA -> AgentContentTextDelta.parseJson(json)
                AgentContentType.TOOL_CALL -> AgentContentToolCall.parseJson(json)
                AgentContentType.TOOL_RESULT -> AgentContentToolResult.parseJson(json)
                AgentContentType.FINISH -> AgentContentFinish.parseJson(json)
                AgentContentType.ERROR -> AgentContentError.parseJson(json)
                AgentContentType.PING -> AgentContentPing.parseJson(json)

                /**
                 * 以下为内部交互使用
                 */
                AgentContentType.EXCEPTION -> AgentContentGalaxyException.parseJson(json)
                AgentContentType.BLOCK_SYSTEM_PROMPT -> AgentContentBlockSystemPrompt.parseJson(json)
                AgentContentType.BLOCK_USER -> AgentContentBlockUser.parseJson(json)
                AgentContentType.BLOCK_TEXT -> AgentContentBlockText.parseJson(json)
                AgentContentType.BLOCK_TOOL_CALL -> AgentContentBlockToolCall.parseJson(json)
                AgentContentType.BLOCK_TOOL_RESULT -> AgentContentBlockToolResult.parseJson(json)
                AgentContentType.BLOCK_DIFY_CALL -> AgentContentBlockDifyCall.parseJson(json)
                AgentContentType.BLOCK_DIFY_RESULT -> AgentContentBlockDifyResult.parseJson(json)
            }
        }

    }

}

class AgentContentText : AgentContentProtocol(AgentContentType.TEXT) {

    var type: String? = _type.code

    var text: String? = null

    companion object {

        fun parseJson(json: String): AgentContentText {
            return JSON.parseObject(json, AgentContentText::class.java)
        }

    }

}

class AgentContentTextDelta : AgentContentProtocol(AgentContentType.TEXT_DELTA) {

    var type: String? = _type.code

    var textDelta: String? = null

    companion object {

        fun parseJson(json: String): AgentContentTextDelta {
            return JSON.parseObject(json, AgentContentTextDelta::class.java)
        }

    }

}

class AgentContentToolCall : AgentContentProtocol(AgentContentType.TOOL_CALL) {

    var type: String? = _type.code

    var toolCallId: String? = null

    var toolName: String? = null

    var args: Map<String, Any>? = null

    companion object {

        fun parseJson(json: String): AgentContentToolCall {
            return JSON.parseObject(json, AgentContentToolCall::class.java)
        }

    }

}

class AgentContentToolResult : AgentContentProtocol(AgentContentType.TOOL_RESULT) {

    var type: String? = _type.code

    var toolCallId: String? = null

    var toolName: String? = null

    var args: Map<String, Any>? = null

    var result: AgentToolResultDetail? = null

    companion object {

        fun parseJson(json: String): AgentContentToolResult {
            return JSON.parseObject(json, AgentContentToolResult::class.java)
        }

    }
}

class AgentToolResultDetail {

    @JSONField(name = "isError")
    var isError: Boolean? = null

    var content: List<Map<String, Any>>? = null

}


class AgentContentFinish : AgentContentProtocol(AgentContentType.FINISH) {

    var type: String? = _type.code

    companion object {

        fun parseJson(json: String): AgentContentFinish {
            return JSON.parseObject(json, AgentContentFinish::class.java)
        }

    }
}

class AgentContentError : AgentContentProtocol(AgentContentType.ERROR) {

    var type: String? = _type.code

    var error: Any? = null

    companion object {

        fun parseJson(json: String): AgentContentError {
            return JSON.parseObject(json, AgentContentError::class.java)
        }

    }
}

class AgentContentPing : AgentContentProtocol(AgentContentType.PING) {

    var type: String? = _type.code

    var ts: Long? = null

    companion object {

        fun parseJson(json: String): AgentContentPing {
            return JSON.parseObject(json, AgentContentPing::class.java)
        }

    }
}

/**
 * 以下为内部交互使用
 */
class AgentContentGalaxyException : AgentContentProtocol(AgentContentType.EXCEPTION) {

    var type: String? = _type.code

    var errorCode: ErrorCode? = null

    var errorMessage: String? = null

    companion object {

        fun parseJson(json: String): AgentContentGalaxyException {
            return JSON.parseObject(json, AgentContentGalaxyException::class.java)
        }

    }

}

class AgentContentBlockSystemPrompt : AgentContentProtocol(AgentContentType.BLOCK_SYSTEM_PROMPT) {

    var type: String? = _type.code

    var content: String? = null

    companion object {

        fun parseJson(json: String): AgentContentBlockSystemPrompt {
            return JSON.parseObject(json, AgentContentBlockSystemPrompt::class.java)
        }

    }

}

class AgentContentBlockUser : AgentContentProtocol(AgentContentType.BLOCK_USER) {

    var type: String? = _type.code

    var content: String? = null

    companion object {

        fun parseJson(json: String): AgentContentBlockUser {
            return JSON.parseObject(json, AgentContentBlockUser::class.java)
        }

    }

}

class AgentContentBlockText : AgentContentProtocol(AgentContentType.BLOCK_TEXT) {

    var type: String? = _type.code

    companion object {

        fun parseJson(json: String): AgentContentBlockText {
            return JSON.parseObject(json, AgentContentBlockText::class.java)
        }

    }

}

class AgentContentBlockToolCall : AgentContentProtocol(AgentContentType.BLOCK_TOOL_CALL) {

    var type: String? = _type.code

    companion object {

        fun parseJson(json: String): AgentContentBlockToolCall {
            return JSON.parseObject(json, AgentContentBlockToolCall::class.java)
        }

    }

}

class AgentContentBlockToolResult : AgentContentProtocol(AgentContentType.BLOCK_TOOL_RESULT) {

    var type: String? = _type.code

    companion object {

        fun parseJson(json: String): AgentContentBlockToolResult {
            return JSON.parseObject(json, AgentContentBlockToolResult::class.java)
        }

    }

}

class AgentContentBlockDifyCall : AgentContentProtocol(AgentContentType.BLOCK_DIFY_CALL) {

    var type: String? = _type.code

    var args: DifyRequestVo? = null

    companion object {

        fun parseJson(json: String): AgentContentBlockDifyCall {
            return JSON.parseObject(json, AgentContentBlockDifyCall::class.java)
        }

    }

}

class AgentContentBlockDifyResult : AgentContentProtocol(AgentContentType.BLOCK_DIFY_RESULT) {

    var type: String? = _type.code

    var result: DifyResponseVo? = null

    companion object {

        fun parseJson(json: String): AgentContentBlockDifyResult {
            return JSON.parseObject(json, AgentContentBlockDifyResult::class.java)
        }

    }

}