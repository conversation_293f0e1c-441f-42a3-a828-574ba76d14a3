package com.ximalaya.galaxy.business.service.impl

import com.ximalaya.galaxy.business.common.APPLICATION_JSON
import com.ximalaya.galaxy.business.common.CONTENT_TYPE
import com.ximalaya.galaxy.business.service.AgentService
import com.ximalaya.galaxy.business.service.configuration.props.GalaxyProperties
import com.ximalaya.galaxy.business.service.support.HttpSupport
import com.ximalaya.galaxy.business.service.vo.AgentBlockChatRequestVo
import com.ximalaya.galaxy.business.service.vo.AgentBlockResponseVo
import com.ximalaya.galaxy.business.service.vo.AgentChatRequestVo
import okhttp3.Callback
import okhttp3.Headers
import okhttp3.OkHttpClient
import okhttp3.Request
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

/**
 *<AUTHOR>
 *@create 2025-05-21 15:59
 */
@Service
class AgentServiceImpl(
    private val galaxyProperties: GalaxyProperties,
    @Qualifier("agentHttpClient") private val httpClient: OkHttpClient,
) : AgentService {

    override fun streamCall(vo: AgentChatRequestVo, callback: Callback) {
        val headers = Headers.Builder()
            .add(CONTENT_TYPE, APPLICATION_JSON)
            .build()

        val httpRequest = Request.Builder()
            .url(galaxyProperties.agent!!.path + "/api/stream/completions")
            .headers(headers)
            .post(vo.toRequestBody())
            .build()

        httpClient.newCall(httpRequest).enqueue(callback)
    }

    override fun blockCall(vo: AgentBlockChatRequestVo): AgentBlockResponseVo {
        val headers = Headers.Builder()
            .add(CONTENT_TYPE, APPLICATION_JSON)
            .build()

        val httpRequest = Request.Builder()
            .url(galaxyProperties.agent!!.path + "/api/block/completions")
            .headers(headers)
            .post(vo.toRequestBody())
            .build()

        val result = HttpSupport.syncCallDify(httpClient, httpRequest) { AgentBlockResponseVo.parseJson(it) }
        return result
    }

}