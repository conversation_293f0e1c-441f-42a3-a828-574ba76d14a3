package com.ximalaya.galaxy.business.service.enums

import com.ximalaya.galaxy.business.common.enums.Enums

/**
 *<AUTHOR>
 *@create 2025-05-21 11:24
 */
enum class AgentMessageRole(
  val code: String,
  val desc: String
) {

  USER("user", "用户"),
  GPT("assistant", "GPT 和 tool-call"),
  TOOL_RESULT("tool", "工具"),
  ;

}

enum class AgentContentType(
  val code: String,
  val desc: String
) {

  TEXT("text", "文本"),
  TEXT_DELTA("text-delta", "文本delta"),
  TOOL_CALL("tool-call", "工具调用"),
  TOOL_RESULT("tool-result", "工具结果"),
  FINISH("finish", "结束"),
  ERROR("error", "出错"),
  PING("ping", "PING"),

  /**
   * 以下为内部交互使用
   */
  EXCEPTION("Exception", "galaxy运行时异常"),
  BLOCK_SYSTEM_PROMPT("Block-system-prompt", "数据库-系统提示词"),
  BLOCK_USER("Block-user", "数据库-用户"),
  BLOCK_TEXT("Block-text", "数据块-文本"),
  BLOCK_TOOL_CALL("Block-tool-call", "数据块-工具调用"),
  BLOCK_TOOL_RESULT("Block-tool-result", "数据块-工具结果"),
  BLOCK_DIFY_CALL("Block-dify-call", "数据块-dify调用"),
  BLOCK_DIFY_RESULT("Block-dify-result", "数据块-dify结果"),
  ;

  companion object {

    @JvmStatic
    fun parseCode(code: String?): AgentContentType {
      return Enums.parseNotNull(
        AgentContentType.values(),
        AgentContentType::code,
        code,
        "Enum not support AgentContentType: $code"
      )
    }

  }
}