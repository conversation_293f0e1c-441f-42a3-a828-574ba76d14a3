package com.ximalaya.galaxy.business.service.configuration.props

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.NestedConfigurationProperty

/**
 *<AUTHOR>
 *@create 2025-05-08 22:23
 */
@ConfigurationProperties(prefix = "galaxy")
class GalaxyProperties {

    lateinit var homePagePath: String

    lateinit var securityKey: String

    @NestedConfigurationProperty
    var agent: GalaxyAgentProperties? = null

    @NestedConfigurationProperty
    var dify: GalaxyDifyProperties? = null

}