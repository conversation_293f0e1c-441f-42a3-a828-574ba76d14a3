package com.ximalaya.galaxy.business.service.configuration

import okhttp3.OkHttpClient
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2023-08-26 11:00
 */
@Configuration
class OkHttpClientConfiguration {

  @Bean(name = ["homePageHttpClient"])
  fun homePageHttpClient(): OkHttpClient {
    return OkHttpClient.Builder()
      // 设置交互整体不超过 5s
      .callTimeout(Duration.ofSeconds(5))
      // 设置连接超时时间 3s
      .connectTimeout(3, TimeUnit.SECONDS)
      // 设置读取超时时间 3s
      .readTimeout(3, TimeUnit.SECONDS)
      // 设置写入超时时间 3s
      .writeTimeout(3, TimeUnit.SECONDS)
      .build()
  }

  @Bean(name = ["difyHttpClient"])
  fun difyHttpClient(): OkHttpClient {
    return OkHttpClient.Builder()
      // 设置交互整体不超过 10min
      .callTimeout(Duration.ofMinutes(10))
      // 设置连接超时时间 30s
      .connectTimeout(30, TimeUnit.SECONDS)
      // 设置读取超时时间 8min
      .readTimeout(8, TimeUnit.MINUTES)
      // 设置写入超时时间 1min
      .writeTimeout(1, TimeUnit.MINUTES)
      .build()
  }

  @Bean(name = ["agentHttpClient"])
  fun agentHttpClient(): OkHttpClient {
    return OkHttpClient.Builder()
      // 设置连接超时时间 30s
      .connectTimeout(30, TimeUnit.SECONDS)
      // 设置读取超时时间 1min
      .readTimeout(2, TimeUnit.MINUTES)
      // 设置写入超时时间 1min
      .writeTimeout(1, TimeUnit.MINUTES)
      .build()
  }

}