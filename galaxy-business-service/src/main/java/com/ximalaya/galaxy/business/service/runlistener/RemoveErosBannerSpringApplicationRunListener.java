package com.ximalaya.galaxy.business.service.runlistener;

import org.springframework.boot.ConfigurableBootstrapContext;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringApplicationRunListener;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * 移除 Eros banner
 *
 * <AUTHOR>
 * @create 2022-05-20 17:01
 */
@Order
public class RemoveErosBannerSpringApplicationRunListener implements SpringApplicationRunListener {

  public RemoveErosBannerSpringApplicationRunListener(SpringApplication application, String[] args) {
  }

  @Override
  public void environmentPrepared(ConfigurableBootstrapContext bootstrapContext, ConfigurableEnvironment environment) {
    System.clearProperty("spring.banner.location");
  }

}
