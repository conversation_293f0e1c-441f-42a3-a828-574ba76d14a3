package com.ximalaya.galaxy.business.service.configuration.props

import org.springframework.boot.context.properties.NestedConfigurationProperty

/**
 *<AUTHOR>
 *@create 2025-05-08 22:23
 */
class GalaxyDifyProperties {

    lateinit var path: String

    @NestedConfigurationProperty
    var appSecret: GalaxyDifyAppSecretProperties? = null

    fun getRegenerateParentTitle() = appSecret?.regenerateParentTitle

    fun getRegenerateTrackName() = appSecret?.regenerateTrackName

}