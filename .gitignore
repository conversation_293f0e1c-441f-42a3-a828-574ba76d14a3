#---------------------------------------#
# Project Ignores                       #
#---------------------------------------#

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

!/.mvn/wrapper/maven-wrapper.jar

# Compiled class file
*.class

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# misc
*.log
log/
logs/


#---------------------------------------#
# IDEs & Editors Ignores                #
#---------------------------------------#

# Sublime Text
/*.sublime*
.sublime-gulp.cache

# VS Code
/.vscode

# JetBrains IDEs
.idea/
*.iml

# Eclipse
.project
.classpath
.settings

# Netbeans
nbactions*.xml
nb-configuration*.xml

# BlueJ
*.ctxt


#---------------------------------------#
# General Ignores                       #
#---------------------------------------#

*~
*.orig
.vagrant


#---------------------------------------#
# Linux Ignores                         #
#---------------------------------------#

# KDE directory preferences
.directory


#---------------------------------------#
# OSX Ignores                           #
#---------------------------------------#

.DS_Store
.AppleDouble
.LSOverride
.localized

# Icon must end with two \r
Icon


# Thumbnails
._*

# Files that might appear on external disk
.Spotlight-V100
.Trashes

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk


#---------------------------------------#
# Windows Ignores                       #
#---------------------------------------#

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/